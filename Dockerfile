FROM debian:bullseye

SHELL ["/bin/bash","-l","-c"]

# Install dependencies
RUN apt-get update
RUN apt-get install ghostscript shared-mime-info openssl curl gnupg2 dirmngr git-core libcurl4-openssl-dev software-properties-common zlib1g-dev build-essential libssl1.1 libreadline-dev libyaml-dev libsqlite3-dev sqlite3 libxml2-dev libxslt1-dev libffi-dev libpq-dev libmagickcore-6.q16-dev -y

# Install rbenv & ruby-build
RUN git clone https://github.com/rbenv/rbenv.git ~/.rbenv
RUN echo 'export PATH="~/.rbenv/bin:$PATH"' >> ~/.bashrc
RUN echo 'eval "$(rbenv init -)"' >> ~/.bashrc

RUN git clone https://github.com/rbenv/ruby-build.git ~/.rbenv/plugins/ruby-build
RUN echo 'export PATH="$HOME/.rbenv/plugins/ruby-build/bin:$PATH"' >> ~/.bashrc

# Install ruby
RUN rbenv rehash
RUN rbenv install 2.3.0
RUN rbenv global 2.3.0

# Set the working directory in the container
WORKDIR /app

# Install specific version of Node.js
RUN curl -sL https://deb.nodesource.com/setup_18.x | bash -

# Install dependencies
RUN apt-get update -qq && apt-get install -y \
    build-essential \
    postgresql-client \
    nodejs

# Install bundler
RUN gem install bundler -v 1.17.3
RUN echo 'export PATH="$(ruby -e "puts Gem.user_dir")/bin:$PATH"' >> ~/.bashrc

# Add the location of the bundler executable to the $PATH
ENV PATH /root/.rbenv/versions/2.3.0/bin:$PATH

# Copy the Gemfile and Gemfile.lock into the container
COPY Gemfile Gemfile.lock ./

# Install project dependencies
RUN bundle config --global silence_root_warning 1
RUN bundle install

# Copy the rest of the application code into the container
COPY . .

# Expose port 3000 for the Rails server
EXPOSE 3000

# Start the Rails server
CMD ["bundle", "exec", "rails", "server", "-b", "0.0.0.0"]