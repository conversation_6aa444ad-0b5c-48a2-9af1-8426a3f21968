source 'https://rubygems.org'

ruby '2.3.0'
group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platform: :mri
  gem 'dotenv-rails', require: 'dotenv/rails-now'
end
# Bundle edge Rails instead: gem 'rails', github: 'rails/rails'
gem 'rails', '~> 5.0.0', '>= *******'
# Use postgresql as the database for Active Record
gem 'pg', '~> 0.18'
# Use Puma as the app server
gem 'puma', '~> 3.0'
# Use SCSS for stylesheets
gem 'sass-rails', '~> 5.0'
# Use Uglifier as compressor for JavaScript assets
gem 'uglifier', '>= 1.3.0'
# Use CoffeeScript for .coffee assets and views
gem 'coffee-rails', '~> 4.2'
# See https://github.com/rails/execjs#readme for more supported runtimes
# gem 'therubyracer', platforms: :ruby

# Use jquery as the JavaScript library
gem 'jquery-rails'
# Turbolinks makes navigating your web application faster. Read more: https://github.com/turbolinks/turbolinks
gem 'turbolinks', '~> 5'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
gem 'jbuilder', '~> 2.5'
# Use Redis adapter to run Action Cable in production
# gem 'redis', '~> 3.0'
# Use ActiveModel has_secure_password
# gem 'bcrypt', '~> 3.1.7'

# Use Capistrano for deployment
# gem 'capistrano-rails', group: :development

gem 'jquery-turbolinks'

gem 'semantic-ui-sass', '2.2.4'
gem 'slim-rails', '3.1.0'
gem 'devise', '4.2.0'
gem 'cancancan'
gem 'rolify'
gem "sentry-raven"
gem 'elasticsearch-transport-aws4'

gem 'devise-two-factor', '~> 4.1.0'
gem 'rqrcode', '~> 2.1.0'
gem 'base32'

gem 'jwt'
gem 'paperclip'
gem 'aws-sdk', '~> 2.3'
gem 'ace-rails-ap'
gem 'graphql-client'
gem 'switch_user'
gem 'react-rails'
gem 'webpacker', git: 'https://github.com/rails/webpacker.git'

group :development do
  # Access an IRB console on exception pages or by using <%= console %> anywhere in the code.
  gem 'web-console'
  gem 'listen', '~> 3.0.5'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
  gem 'spring-watcher-listen', '~> 2.0.0'
  # Guard 
  gem 'guard', require: false
  gem 'guard-bundler', require: false
  gem 'guard-rails', require: false
  gem 'guard-livereload', '~> 2.5', require: false
  gem 'growl', require: false
  gem 'terminal-notifier-guard', require: false
  gem 'rb-readline', require: false
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: [:mingw, :mswin, :x64_mingw, :jruby]
