GIT
  remote: https://github.com/rails/webpacker.git
  revision: b2d899b25fb9f1cb11426b1b5e2d699c680bdcf6
  specs:
    webpacker (3.0.2)
      activesupport (>= 4.2)
      rack-proxy (>= 0.6.1)
      railties (>= 4.2)

GEM
  remote: https://rubygems.org/
  specs:
    ace-rails-ap (4.1.1)
    actioncable (*******)
      actionpack (= *******)
      nio4r (~> 1.2)
      websocket-driver (~> 0.6.1)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0)
      rack-test (~> 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubis (~> 2.7.0)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      arel (~> 7.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (~> 0.7)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
    arel (7.1.1)
    attr_encrypted (3.1.0)
      encryptor (~> 3.0.0)
    aws-sdk (2.6.0)
      aws-sdk-resources (= 2.6.0)
    aws-sdk-core (2.6.0)
      jmespath (~> 1.0)
    aws-sdk-resources (2.6.0)
      aws-sdk-core (= 2.6.0)
    babel-source (5.8.35)
    babel-transpiler (0.7.0)
      babel-source (>= 4.0, < 6)
      execjs (~> 2.0)
    base32 (0.3.4)
    bcrypt (3.1.16)
    builder (3.2.2)
    byebug (9.0.5)
    cancancan (1.15.0)
    chunky_png (1.4.0)
    climate_control (0.2.0)
    cocaine (0.5.8)
      climate_control (>= 0.0.3, < 1.0)
    coderay (1.1.1)
    coffee-rails (4.2.1)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0, < 5.2.x)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.10.0)
    concurrent-ruby (1.0.2)
    connection_pool (2.2.1)
    debug_inspector (0.0.2)
    devise (4.2.0)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0, < 5.1)
      responders
      warden (~> 1.2.3)
    devise-two-factor (4.1.0)
      activesupport (< 7.1)
      attr_encrypted (>= 1.3, < 5, != 2)
      devise (~> 4.0)
      railties (< 7.1)
      rotp (~> 6.0)
    dotenv (2.1.1)
    dotenv-rails (2.1.1)
      dotenv (= 2.1.1)
      railties (>= 4.0, < 5.1)
    elasticsearch-transport (2.0.2)
      faraday
      multi_json
    elasticsearch-transport-aws4 (2.0.0)
      aws-sdk (~> 2.1)
      elasticsearch-transport (~> 2.0)
    em-websocket (0.5.1)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0.6.0)
    encryptor (3.0.0)
    erubis (2.7.0)
    eventmachine (*******)
    execjs (2.7.0)
    faraday (0.12.1)
      multipart-post (>= 1.2, < 3)
    ffi (1.9.14)
    formatador (0.2.5)
    globalid (0.3.7)
      activesupport (>= 4.1.0)
    graphql (1.5.10)
    graphql-client (0.8.5)
      activesupport (>= 3.0, < 6.0)
      graphql (~> 1.2)
    growl (1.0.3)
    guard (2.14.0)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      lumberjack (~> 1.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      pry (>= 0.9.12)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-bundler (2.1.0)
      bundler (~> 1.0)
      guard (~> 2.2)
      guard-compat (~> 1.1)
    guard-compat (1.2.1)
    guard-livereload (2.5.2)
      em-websocket (~> 0.5)
      guard (~> 2.8)
      guard-compat (~> 1.0)
      multi_json (~> 1.8)
    guard-rails (0.8.0)
      guard (~> 2.11)
      guard-compat (~> 1.0)
    http_parser.rb (0.6.0)
    i18n (0.7.0)
    jbuilder (2.6.0)
      activesupport (>= 3.0.0, < 5.1)
      multi_json (~> 1.2)
    jmespath (1.3.1)
    jquery-rails (4.2.1)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jquery-turbolinks (2.1.0)
      railties (>= 3.1.0)
      turbolinks
    jwt (1.5.4)
    listen (3.0.8)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    loofah (2.0.3)
      nokogiri (>= 1.5.9)
    lumberjack (1.0.10)
    mail (2.6.4)
      mime-types (>= 1.16, < 4)
    method_source (0.8.2)
    mime-types (3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2016.0521)
    mimemagic (0.3.10)
      nokogiri (~> 1)
      rake
    mini_portile2 (2.1.0)
    minitest (5.9.0)
    multi_json (1.12.1)
    multipart-post (2.0.0)
    nenv (0.3.0)
    nio4r (1.2.1)
    nokogiri (1.6.8)
      mini_portile2 (~> 2.1.0)
      pkg-config (~> 1.1.7)
    notiffany (0.1.1)
      nenv (~> 0.1)
      shellany (~> 0.0)
    orm_adapter (0.5.0)
    paperclip (5.1.0)
      activemodel (>= 4.2.0)
      activesupport (>= 4.2.0)
      cocaine (~> 0.5.5)
      mime-types
      mimemagic (~> 0.3.0)
    pg (0.18.4)
    pkg-config (1.1.7)
    pry (0.10.4)
      coderay (~> 1.1.0)
      method_source (~> 0.8.1)
      slop (~> 3.4)
    puma (3.6.0)
    rack (2.0.1)
    rack-proxy (0.6.2)
      rack
    rack-test (0.6.3)
      rack (>= 1.0)
    rails (*******)
      actioncable (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activesupport (= *******)
      bundler (>= 1.3.0, < 2.0)
      railties (= *******)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.0.1)
      activesupport (>= 4.2.0, < 6.0)
      nokogiri (~> 1.6.0)
    rails-html-sanitizer (1.0.3)
      loofah (~> 2.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 0.8.7)
      thor (>= 0.18.1, < 2.0)
    rake (11.2.2)
    rb-fsevent (0.9.7)
    rb-inotify (0.9.7)
      ffi (>= 0.5.0)
    rb-readline (0.5.5)
    react-rails (2.4.0)
      babel-transpiler (>= 0.7.0)
      connection_pool
      execjs
      railties (>= 3.2)
      tilt
    responders (2.3.0)
      railties (>= 4.2.0, < 5.1)
    rolify (5.1.0)
    rotp (6.3.0)
    rqrcode (2.1.2)
      chunky_png (~> 1.0)
      rqrcode_core (~> 1.0)
    rqrcode_core (1.2.0)
    sass (3.4.22)
    sass-rails (5.0.6)
      railties (>= 4.0.0, < 6)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    semantic-ui-sass (2.2.4.0)
      sass (>= 3.2)
    sentry-raven (2.4.0)
      faraday (>= 0.7.6, < 1.0)
    shellany (0.0.1)
    slim (3.0.7)
      temple (~> 0.7.6)
      tilt (>= 1.3.3, < 2.1)
    slim-rails (3.1.0)
      actionpack (>= 3.1)
      railties (>= 3.1)
      slim (~> 3.0)
    slop (3.6.0)
    spring (1.7.2)
    spring-watcher-listen (2.0.0)
      listen (>= 2.7, < 4.0)
      spring (~> 1.2)
    sprockets (3.7.0)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.1.1)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    switch_user (1.4.0)
    temple (0.7.7)
    terminal-notifier-guard (1.7.0)
    thor (0.19.1)
    thread_safe (0.3.5)
    tilt (2.0.5)
    turbolinks (5.0.1)
      turbolinks-source (~> 5)
    turbolinks-source (5.0.0)
    tzinfo (1.2.2)
      thread_safe (~> 0.1)
    uglifier (3.0.2)
      execjs (>= 0.3.0, < 3)
    warden (1.2.6)
      rack (>= 1.0)
    web-console (3.3.1)
      actionview (>= 5.0)
      activemodel (>= 5.0)
      debug_inspector
      railties (>= 5.0)
    websocket-driver (0.6.4)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.2)

PLATFORMS
  ruby

DEPENDENCIES
  ace-rails-ap
  aws-sdk (~> 2.3)
  base32
  byebug
  cancancan
  coffee-rails (~> 4.2)
  devise (= 4.2.0)
  devise-two-factor (~> 4.1.0)
  dotenv-rails
  elasticsearch-transport-aws4
  graphql-client
  growl
  guard
  guard-bundler
  guard-livereload (~> 2.5)
  guard-rails
  jbuilder (~> 2.5)
  jquery-rails
  jquery-turbolinks
  jwt
  listen (~> 3.0.5)
  paperclip
  pg (~> 0.18)
  puma (~> 3.0)
  rails (~> 5.0.0, >= *******)
  rb-readline
  react-rails
  rolify
  rqrcode (~> 2.1.0)
  sass-rails (~> 5.0)
  semantic-ui-sass (= 2.2.4)
  sentry-raven
  slim-rails (= 3.1.0)
  spring
  spring-watcher-listen (~> 2.0.0)
  switch_user
  terminal-notifier-guard
  turbolinks (~> 5)
  tzinfo-data
  uglifier (>= 1.3.0)
  web-console
  webpacker!

RUBY VERSION
   ruby 2.3.0p0

BUNDLED WITH
   1.17.3
