{"name": "quotelier-rails", "scripts": {"postdeploy": "./bootstrap.sh"}, "env": {"AWS_ACCESS_KEY_ID": {"required": true}, "AWS_SECRET_ACCESS_KEY": {"required": true}, "HMAC": {"required": true}, "LANG": {"required": true}, "RACK_ENV": {"required": true}, "RAILS_ENV": {"required": true}, "RAILS_LOG_TO_STDOUT": {"required": true}, "RAILS_SERVE_STATIC_FILES": {"required": true}, "REACTAPP": {"required": true, "value": "https://dev-assets.quotelier.net"}, "REGION": {"required": true}, "SECRET_KEY_BASE": {"required": true}, "QUOTELIER_TOKEN": {"required": true}, "GRAPHQL_ENDPOINT": {"required": true, "value": "https://api.quotelier.net/devel/graphql"}, "STAGE": {"required": true, "value": "devel"}, "CACHE_BUCKET": {"required": true, "value": "integrations-devel-cache"}, "FILES_BUCKET": {"required": true, "value": "files.quotelier.net"}, "OPERATORS_BUCKET": {"required": true, "value": "operators-service-devel-operators"}, "TEMPLATES_BUCKET": {"required": true, "value": "properties-service-devel-templates"}, "PROPERTIES_BUCKET": {"required": true, "value": "properties-service-devel-properties"}, "INITIALIZE_ACCOUNT_FUNCTION": {"required": true, "value": "integrations-devel-createAccount"}, "INITIALIZE_PROPERTY_FUNCTION": {"required": true, "value": "properties-service-devel-initializeProperty"}, "INITIALIZE_UPSALE_FUNCTION": {"required": true, "value": "upsales-devel-initialize"}, "CLEAR_CACHE_FUNCTION": {"required": true, "value": "integrations-devel-clearCache"}, "ES_ENDPOINT": {"required": true, "value": "https://search-elasticsearch-quote-indexing-j6vs7kqq6lssesys7hqgrrqdoi.eu-central-1.es.amazonaws.com/"}, "ES_ACCESS_KEY_ID": {"required": true, "value": "********************"}, "ES_SECRET_ACCESS_KEY": {"required": true, "value": "VhNxHKcZlVbFc8JNK1FRdV2T2HJfvQpYxMPHpQa7"}, "INTERCOM_HASH": {"required": true, "value": "s0F5EyZS2ulfgFOc66KnuyCe5k4lvTCicrBqX-Aw"}, "REWUNDO_HOST": {"required": true, "value": "https://dev.rewundo.net"}, "REWUNDO_APIKEY": {"required": true, "value": "a0b1c2d3e4"}}, "formation": {}, "addons": ["heroku-postgresql"], "stack": "heroku-16", "buildpacks": [{"url": "hero<PERSON>/nodejs"}, {"url": "heroku/ruby"}]}