# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/

$(document).on 'ready turbolinks:load', ->
  editor = $('#ace-editor')
  if editor.length > 0
    is_template = $(editor).hasClass 'templates'
    is_emailTemplate = $(editor).hasClass 'emailTemplates'
    is_partial = $(editor).hasClass 'partials'
    editor = ace.edit('ace-editor')
    # editor.setTheme 'ace/theme/monokai'
    JsonMode = ace.require('ace/mode/json').Mode
    TemplateMode = ace.require('ace/mode/handlebars').Mode
    if is_template || is_emailTemplate || is_partial
      editor.session.setMode new TemplateMode
    else
      editor.session.setMode new JsonMode
    editor.session.setUseWrapMode true
    editor.setReadOnly false

    textarea = $('textarea[name="body"]')
    editor.getSession().on 'changeAnnotation', ->
      annotations = editor.getSession().getAnnotations()
      if annotations.length > 0
        $('input[type=submit]').addClass 'disabled'
      else
        $('input[type=submit]').removeClass 'disabled'

    editor.getSession().on 'change', ->
      textarea.val editor.getSession().getValue()
      return