# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/

# $(document).on 'ready turbolinks:load', ->
#   $('.ui.dropdown').dropdown()

# $(document).on 'ready', ->
#   $('.message .close').on 'click', ->
#     $(this).closest('.message').transition 'fade'
#     return