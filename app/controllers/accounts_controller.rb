class AccountsController < ApplicationController
  load_and_authorize_resource
  def index
    
  end

  def new
    @account = Account.new
  end

  def create
    @account = Account.new(account_params)
    if @account.save
      redirect_to :accounts, notice: 'Account was successfully created.'
    else
      render :new
    end
  end

  def destroy
    @account = Account.find(params[:account])
    if @account 
      @account.destroy
      flash[:information] = 'Account was removed.'
    end
    redirect_to :accounts
  end

  def initialize_caching
    @account = Account.find(params[:account_id])
    properties = Property.where(:name => params[:property_code])
    @property = properties.first
  end

  def initialize_caching_update
    client = Aws::Lambda::Client.new(region: ENV['REGION'])

    camelized_params = params[:initialize_caching_update].to_unsafe_hash.transform_keys{ |key| key.to_s.camelize(:lower)}
    args = camelized_params.to_json
    account_id = params[:account_id]
    property_code = params[:initialize_caching_update][:property_code]
    account = Account.find(account_id)
    property = Property.find_or_create_by({name: property_code, code: property_code, account_id: account_id})
    
    begin
      resp = client.invoke_async(function_name: ENV['INITIALIZE_PROPERTY_FUNCTION'], invoke_args: args)
      if resp.status != 202
        flash[:error] = "Caching failed"
      end
      clear_cache(account, property)

      redirect_to account_properties_path(account_id), notice: "Caching initialized successfully"
    rescue Aws::Lambda::Errors::ServiceError => e
      puts e.message
    end

  end

  private

    def clear_cache(account, property)
      path = "#{account.name}/#{property.name}"
      delete_folder_from(path, ENV['CACHE_BUCKET'])
    end

    def create_operator_for(account)
      path = Rails.root.join('test/fixtures/files/MARINA.json')
      if File.exist?(path)
        key  = "#{account}/en/MARINA"

        s3 = Aws::S3::Client.new(region: ENV['REGION'])
        File.open(path, 'rb') do |file|
          s3.put_object(bucket: ENV['OPERATORS_BUCKET'], key: key, body: file)
        end
      end
    end

    def cache_assets_for(account)
      client = Aws::Lambda::Client.new(region: ENV['REGION'])
      camelized_params = account.transform_keys{ |key| key.to_s.camelize(:lower)}
      args = camelized_params.to_json
      
      begin
        resp = client.invoke_async(function_name: ENV['INITIALIZE_ACCOUNT_FUNCTION'], invoke_args: args)
        if resp.status != 202
          flash[:error] = "Account creation failed"
        end
      rescue Aws::Lambda::Errors::ServiceError => e
        puts e.message
      end
    end

    def account_params
      params.require(:account).permit(:name)
    end
end
