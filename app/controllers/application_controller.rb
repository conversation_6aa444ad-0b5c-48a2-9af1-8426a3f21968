class ApplicationController < ActionController::Base
  before_action :set_raven_context
  before_action :configure_permitted_parameters, if: :devise_controller?

  protect_from_forgery with: :exception

  class QueryError < StandardError; end

  def current_ability
    @current_ability ||= current_admin ? Ability.new(current_admin) : Ability.new(current_user)
  end

  def after_sign_in_path_for(resource)
    user_otp_path
  end

  rescue_from CanCan::AccessDenied do |exception|
    redirect_to "/550.html"
  end

  layout :layout_by_resource

  protected

    def layout_by_resource
      if devise_controller?
        "devise"
      else
        "application"
      end
    end

    def configure_permitted_parameters
      devise_parameter_sanitizer.permit(:sign_in, keys: [:otp_attempt])
    end

  private
    # Public: Define request scoped helper method for making GraphQL queries.
    #
    # Examples
    #
    #   data = query(ViewerQuery)
    #   data.viewer.login #=> "josh"
    #
    # definition - A query or mutation operation GraphQL::Client::Definition.
    #              Client.parse("query { version }") returns a definition.
    # variables - Optional set of variables to use during the operation.
    #             (default: {})
    #
    # Returns a structured query result or raises if the request failed.
    def query(definition, variables = {}, client_context = {} )
      response = Quotelier::Client.query(definition, variables: variables, context: client_context)

      if response.errors.any?
        raise QueryError.new(response.errors[:data].join(", "))
      else
        response.data
      end
    end

    def set_raven_context
      Raven.user_context(id: session[:current_user_id]) # or anything else in session
      Raven.extra_context(params: params.to_unsafe_h, url: request.url)
    end

    def get_asset_from(asset, bucket)
      s3 = Aws::S3::Client.new(region: ENV['REGION'])
  
      begin
        asset = s3.get_object(bucket: bucket, key: asset)
      rescue Aws::S3::Errors::ServiceError => e
        puts e.message
      end
  
      asset.try(:body).try(:read) || {data: 'nil'}.to_json
    end

    def delete_asset_from(asset, bucket)
      s3 = Aws::S3::Client.new(region: ENV['REGION'])
  
      begin
        asset = s3.delete_object(bucket: bucket, key: asset)
      rescue Aws::S3::Errors::ServiceError => e
        puts e.message
      end
    end

    def delete_folder_from(folder, bucket)
      s3 = Aws::S3::Client.new(region: ENV['REGION'])
  
      begin
        objects = s3.list_objects_v2(bucket: bucket, prefix: folder)
        objects.contents.each do |object|
          delete_asset_from(object.key, bucket)
        end
      rescue Aws::S3::Errors::ServiceError => e
        puts e.message
      end
    end

    def put_asset_to(data, asset, bucket)
      s3 = Aws::S3::Client.new(region: ENV['REGION'])
      
      begin
        resp = s3.put_object(bucket: bucket, key: asset, body: data)
      rescue Aws::S3::Errors::ServiceError => e
        puts e.message
      end
    end

    def copy_asset_to(source, target, bucket)
      @prefix = target[/.*\//]

      s3 = Aws::S3::Client.new(region: ENV['REGION'])
      begin
        asset = s3.copy_object(bucket: bucket, key: target, copy_source: "#{bucket}/#{source}")
      rescue Aws::S3::Errors::ServiceError => e
        puts e.message
      end
    end

    def index_assets(prefix, bucket, type=:folders)
      s3 = Aws::S3::Client.new(region: ENV['REGION'])

      begin
        resp = s3.list_objects_v2(bucket: bucket, prefix: prefix, delimiter:'/')
        unless resp.nil?
          @folders = resp.common_prefixes.map(&:prefix)
          @files   = resp.contents
        else
          @files = []
          @folders = []
        end

        if type == :folders
          folders = @folders
        else
          files = @files
        end
      rescue Aws::S3::Errors::ServiceError => e
        puts e.message
      end
    end
end
