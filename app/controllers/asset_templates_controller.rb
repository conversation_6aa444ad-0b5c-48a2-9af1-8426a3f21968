class AssetTemplatesController < ApplicationController
  authorize_resource
  
  def index
    @prefix = params[:prefix] || ''
    path_items = @prefix.split('/')
    path_items = path_items[0..3]
    path_items[0] = "DEFAULT"
    path_items[2] = "default"
    @default_path = "#{path_items.join('/')}/"
    default_files = index_assets(@default_path, ENV['TEMPLATES_BUCKET'], :files)
    @folders = index_assets(@prefix, ENV['TEMPLATES_BUCKET'], :folders)
    files   = index_assets(@prefix, ENV['TEMPLATES_BUCKET'], :files)
    @files = files.map { |x| x.to_h.merge!(prefix: x.key.slice!(@prefix)) }
    @default_files = default_files.map { |x| x.to_h.merge!(prefix: x.key.slice!(@default_path)) }
    filenames = @files.map{ |x| x[:key] }
    @default_files = @default_files.map { |x| !filenames.include?(x[:key]) ? x : nil }
    @default_files = @default_files.compact
  end

  def show
    @item = params[:item]
    s3 = Aws::S3::Client.new(region: ENV['REGION'])

    begin
      asset = s3.get_object(bucket: ENV['TEMPLATES_BUCKET'], key:@item)
    rescue Aws::S3::Errors::ServiceError => e
      puts e.message
    end
    path_items = @item.split('/')
    @template_type = path_items[3] || ''
    @asset = asset.try(:body).try(:read) || ''
    render formats: [:html, :json]
  end

  def copy
    @source = params[:source]
    @target = params[:target]
    @prefix = @target[/.*\//]

    asset = copy_asset_to(@source, @target, ENV['TEMPLATES_BUCKET'])

    unless asset.nil?
      redirect_to asset_templates_path(@prefix), notice: 'Copied succefully'
    else
      redirect_to asset_templates_path(@prefix), error: 'Something went terrible wrong, will copying the asset!'
    end
  end

  def destroy
    @item = params[:item]
    @prefix = @item[/.*\//]
    resp = delete_asset_from(@item, ENV['TEMPLATES_BUCKET'])
    unless resp.empty?
      redirect_to asset_templates_path(@prefix), notice: 'Deleted succefully'
    else
      redirect_to asset_templates_path(@prefix), error: 'Something went terrible wrong, will deleting the asset!'
    end
  end


  def update
    data = params[:body]
    data = data.empty? ? '' : data
    @item = params[:item] || ''

    s3 = Aws::S3::Client.new(region: ENV['REGION'])

    begin
      asset = s3.put_object(bucket: ENV['TEMPLATES_BUCKET'], key:@item, body: data)
      redirect_to asset_template_path(@item), notice: 'Updated succefully'
    rescue Aws::S3::Errors::ServiceError => e
      puts e.message
    end
  end

  PreviewQuery = Quotelier::Client.parse <<-'GRAPHQL'
    query($request: RequestInput!) {
      requestPreview(request: $request)
    }

  GRAPHQL

  RequestsQuery = Quotelier::Client.parse <<-'GRAPHQL'
    query($state: RequestStateFilter!, $propertyCode: String) {
      requests(state: $state, propertyCode: $propertyCode) {
        operatorId
        template
        language
        offers {
          rateId
          accommodationCodes
          propertyCode
          checkin
          nights
          adults
          children
          infants
          title
          description
          officialRate
          roomRate
          currency
          rooms
        }
        contact {
          firstName
          lastName
          email
          nickname
        }
        request {
          accountName
          propertyCode
          message
          adults
          children
          infants
          checkin
          nights
          rooms
        }
      }
    }

  GRAPHQL

  def preview
    params_item = params[:item]
    path_items = params_item.split('/')
    account = path_items.first || ' '
    propertyCode = path_items[2] || ''
    user = current_user || Account.try(:find_by_name, account).try(:users).try(:first)
    token = user.try(:permissions_token) || ENV['QUOTELIER_TOKEN']
    
    result = query(RequestsQuery, {state: "all", propertyCode: propertyCode}, {permissions_token: token})
    
    if result.requests.empty?
      @html = '<h1>REQUEST DATA NOT FOUND</h1>'
    else
      request_object = result.requests.try(:first) || {template: ''}
      request_object = request_object.to_h
      request_object["template"] = path_items.last

      # Change date-time to YYYY-MM-DD
      checkin = request_object["request"]["checkin"]
      request_object["request"]["checkin"] = checkin.to_datetime.strftime("%F") unless checkin.nil?

      request_object["offers"].each do |o|
        checkin = o["checkin"].to_datetime
        o["checkin"] = checkin.strftime("%F")
      end

      response = query(PreviewQuery, {request: request_object}, {permissions_token: token})
      @html = response.requestPreview
    end
    render :layout => false
  end


end
