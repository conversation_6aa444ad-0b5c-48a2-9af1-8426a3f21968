class AssetsController < ApplicationController
  authorize_resource
  
  def index
    @prefix = params[:prefix] || ''
    @folders = index_assets(@prefix, ENV['PROPERTIES_BUCKET'], :folders)
    @files = index_assets(@prefix, ENV['PROPERTIES_BUCKET'], :files)
  end

  def show
    @item = params[:item]
    path_items = @item.split('/')
    case path_items.count
    when 3
      asset_type = "property"
    when 4
      asset_type = "room"
    when 5
      asset_type = "rate"
    when 6
      asset_type = "service"
    else
      asset_type = "property"
    end

    @asset = get_asset_from(@item, ENV['PROPERTIES_BUCKET'])
    schema = get_asset_from("DEFAULT/en/default/formSchemas/#{asset_type}Schema.json", ENV['TEMPLATES_BUCKET'])
    uiSchema = get_asset_from('DEFAULT/en/default/formSchemas/UISchema.json', ENV['TEMPLATES_BUCKET'])
    formData = @asset
    @props = {schema: JSON.parse(schema), uiSchema: JSON.parse(uiSchema), formData: JSON.parse_nil(formData)}
    render layout: "assets"
  end

  def update
    data = params[:body]
    data = data.empty? ? '{"data": "null"}' : data
    data = JSON.generate(JSON.parse(data))
    @item = params[:item]

    asset = put_asset_to(data, @item, ENV['PROPERTIES_BUCKET'])

    unless asset.nil?
      redirect_to asset_path(@item), notice: 'Updated succefully'
    else
      redirect_to asset_path(@item), error: 'Something went terrible wrong!'
    end
  end

end
