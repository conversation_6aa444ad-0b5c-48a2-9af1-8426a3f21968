class AssetsOperatorsController < ApplicationController
  authorize_resource
  
  def index
    @prefix = params[:prefix] || ''
    @folders = index_assets(@prefix, ENV['OPERATORS_BUCKET'], :folders)
    @files = index_assets(@prefix, ENV['OPERATORS_BUCKET'], :files)
  end

  def show
    @item = params[:item]
    @asset = get_asset_from(@item, ENV['OPERATORS_BUCKET'])
    schema = get_asset_from('DEFAULT/en/default/formSchemas/operatorSchema.json', ENV['TEMPLATES_BUCKET'])
    uiSchema = get_asset_from('DEFAULT/en/default/formSchemas/UISchema.json', ENV['TEMPLATES_BUCKET'])
    formData = @asset
    @props = {schema: JSON.parse(schema), uiSchema: JSON.parse(uiSchema), formData: JSON.parse_nil(formData)}
    render layout: "assets"
  end

  def update
    data = params[:body]
    data = data.empty? ? '{"data": "null"}' : data
    data = JSON.generate(JSON.parse(data))
    @item = params[:item]

    asset = put_asset_to(data, @item, ENV['OPERATORS_BUCKET'])

    unless asset.nil?
      redirect_to asset_operator_path(@item), notice: 'Updated succefully'
    else
      redirect_to asset_operator_path(@item), error: 'Something went terrible wrong!'
    end
  end

  def destroy
    @item = params[:item]
    @prefix = @item[/.*\//]
    resp = delete_asset_from(@item, ENV['OPERATORS_BUCKET'])
    unless resp.empty?
      redirect_to asset_operators_path(@prefix), notice: 'Deleted succefully'
    else
      redirect_to asset_operators_path(@prefix), error: 'Something went terrible wrong, will deleting the asset!'
    end
  end

end

module JSON
  def self.parse_nil(json)
    JSON.parse(json) if json && json.length >= 2
  end
end