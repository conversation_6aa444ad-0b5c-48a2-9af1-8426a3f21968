class AvailabilityController < ApplicationController
  authorize_resource

  def index
    user = current_user || User.find(params[:user]) || 1
    is_admin = current_user.nil? # if user is nil then it means he is an Admin
    if user
      @account = user.account
      @props = Hash.new
      @props = { accountName: @account.name, operatorId: user.email, token: user.permissions_token , isAdmin: is_admin}
      render layout: "availability"
    else
      redirect_to :accounts
    end
  end
end