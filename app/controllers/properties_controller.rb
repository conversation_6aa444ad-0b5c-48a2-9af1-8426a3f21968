class PropertiesController < ApplicationController
  load_and_authorize_resource

  def index
    @properties = Property.where(account_id: params[:account_id])
    @account = Account.find(params[:account_id])
  end

  def new
    @property = { 
      account_name: params[:account_name].try(:upcase),
      type: "webhotelier"
    }
  end

  def destroy
    @property = Property.find(params[:id])
    if @property 
      @property.destroy
      flash[:information] = 'Property was removed.'
    end
    redirect_to [@property.account, :properties]
  end

  def create_initialize
    params[:account_name] = params[:account_name_for]
    @account = Account.find_or_create_by({name: params[:account_name]})
    @property = Property.find_or_create_by({name: params[:property_code], code: params[:property_code], account_id: @account.id})
    client = Aws::Lambda::Client.new(region: ENV['REGION'])
    params.slice!(:account_name, :property_code, :account_password, :type)
    camelized_params = params.transform_keys{ |key| key.to_s.camelize(:lower)}
    args = camelized_params.to_json
    
    begin
      resp = client.invoke_async(function_name: ENV['INITIALIZE_ACCOUNT_FUNCTION'], invoke_args: args)
      if resp.status != 202
        flash[:error] = "Account creation failed"
      end
      redirect_to [@property.account, :properties], notice: 'Property was successfully created.'
    rescue Aws::Lambda::Errors::ServiceError => e
      puts e.message
    end
  end

  def initialize_upsales
    client = Aws::Lambda::Client.new(region: ENV['REGION'])

    account_id = params[:account_id]
    property_code = params[:property_code]
    account_name = params[:account_name]
    
    unless property_code.nil? && account_name.nil?
      camelized_params = {account_name: account_name, property_code: property_code}.transform_keys{ |key| key.to_s.camelize(:lower)}
      args = camelized_params.to_json
      begin
        resp = client.invoke_async(function_name: ENV['INITIALIZE_UPSALE_FUNCTION'], invoke_args: args)
        if resp.status != 202
          flash[:error] = 'Upsales failed to initialize.'
        end
      rescue Aws::Lambda::Errors::ServiceError => e
        puts e.message
      end
    else
      redirect_to account_properties_path(account_id), error: 'Upsales failed to initialize.'
    end

    redirect_to account_properties_path(account_id), notice: 'Upsales was successfully initialized.'
  end

  def clear_cache
    client = Aws::Lambda::Client.new(region: ENV['REGION'])

    account_id = params[:account_id]
    property_code = params[:property_code]
    account_name = params[:account_name]
    
    unless property_code.nil? && account_name.nil?
      camelized_params = {account_name: account_name, property_code: property_code}.transform_keys{ |key| key.to_s.camelize(:lower)}
      args = camelized_params.to_json
      begin
        resp = client.invoke_async(function_name: ENV['CLEAR_CACHE_FUNCTION'], invoke_args: args)
        if resp.status != 202
          flash[:error] = 'Clear Cache Failed.'
        end
      rescue Aws::Lambda::Errors::ServiceError => e
        puts e.message
      end
    else
      redirect_to account_properties_path(account_id), error: 'Clear Cache Failed.'
    end

    redirect_to account_properties_path(account_id), notice: 'Cache clearing is completed.'
  end

  private
    def property_params
      params.require(:property).permit(:name).permit(:account_id)
    end
end
