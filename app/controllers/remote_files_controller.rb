class RemoteFilesController < ApplicationController
  authorize_resource

  def index
    @prefix = params[:prefix] || 'docs/'
    @folders = index_assets(@prefix, ENV['FILES_BUCKET'], :folders)
    @files = index_assets(@prefix, ENV['FILES_BUCKET'], :files)
  end

  def new
    @remote_file = RemoteFile.new
  end

  def create
    @file = RemoteFile.new(remote_file_params)
    if @file.save
      redirect_to :remote_files, notice: 'File uploaded successfully.'
    else
      redirect_to :new_remote_file, warning: 'Upload failed'
    end
  end

  def destroy
    @item = params[:item]
    @prefix = @item[/.*\//]
    resp = delete_asset_from(@item, ENV['FILES_BUCKET'])

    unless resp.empty?
      redirect_to :remote_files, notice: 'File removed successfully.'
    else
      redirect_to remote_files_path(@prefix), error: 'Something went terrible wrong, will deleting the file!'
    end
  end

private
  def remote_file_params
    params.require(:remote_file).permit(:name, :document)
  end

end