class RoleTypesController < ApplicationController
  load_and_authorize_resource

  def index
    @role_types = RoleType.all
  end

  def new
    @role_type = RoleType.new
  end

  def create
    @role_type = RoleType.new(role_type_params)
    if @role_type.save
      redirect_to :role_types, notice: 'Flag was successfully created.'
    else
      render :index
    end
  end

  def destroy
    @role_type = RoleType.find(params[:id])
    if @role_type 
      @role_type.destroy
      flash[:information] = 'Flag was removed.'
    end
    redirect_to :role_types
  end

  def assign_role_to_all_users
    user = User.all
    role = params[:role]
    user.each do |user|
      user.account.properties.each do |property|
        user.add_role role.parameterize.underscore.to_sym, property
      end
    end
    redirect_to :role_types, notice: "#{role.humanize} assigned to all Operators"
  end

  def role_type_params
    params.require(:role_type).permit(:name)
  end
end  