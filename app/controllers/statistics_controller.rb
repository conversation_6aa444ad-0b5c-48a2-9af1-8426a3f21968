class StatisticsController < ApplicationController
  authorize_resource
  skip_before_action :verify_authenticity_token
  def show
    @props = {}
    render layout: "statistics"
  end
  def search
    body = request.raw_post
    client = Elasticsearch::Client.new(
      url: ENV['ES_ENDPOINT'],
      transport_class: Elasticsearch::Transport::AWS4,
      aws4: {
        key: ENV['ES_ACCESS_KEY_ID'],
        secret: ENV['ES_SECRET_ACCESS_KEY'],
        region: ENV['REGION']
      }
    )
    response = client.perform_request 'POST', '_search', {}, body
    render :json => response.body
  end

end