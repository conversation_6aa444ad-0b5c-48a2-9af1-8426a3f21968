class ThemesController < ApplicationController
  authorize_resource
  before_action :load_parents, only: [:index, :new, :create, :destroy]

  def index
    @themes = Theme.where(property_id: params[:property_id])
  end

  def new
    @theme = Theme.new(property_id: params[:property_id])
  end

  def create
    @theme = Theme.new(theme_params)
    bucket = ENV['TEMPLATES_BUCKET']
    if @theme.save
      languages = index_assets("#{@account.name}/", bucket)
      languages.each do |lang|
        lang = lang.split('/').last
        
        source = "DEFAULT/#{lang}/default/templates/proposal.handlebars"
        target = "#{@account.name}/#{lang}/#{@property.name}/templates/#{@theme.name}.handlebars"
        target_data = get_asset_from(target, bucket)
        target_exists = target_data != "{\"data\":\"nil\"}"
        template_asset = copy_asset_to(source, target, bucket) unless target_exists

        source = "DEFAULT/#{lang}/default/partials/layout.handlebars"
        target = "#{@account.name}/#{lang}/#{@property.name}/partials/#{@theme.name}/layout.handlebars"
        target_data = get_asset_from(target, bucket)
        target_exists = target_data != "{\"data\":\"nil\"}"
        template_asset = copy_asset_to(source, target, bucket) unless target_exists
        
        source = "DEFAULT/#{lang}/default/emailTemplates/customerPaymentOptions.handlebars"
        target = "#{@account.name}/#{lang}/#{@property.name}/emailTemplates/#{@theme.name}/customerPaymentOptions.handlebars"
        target_data = get_asset_from(target, bucket)
        target_exists = target_data != "{\"data\":\"nil\"}"
        template_asset = copy_asset_to(source, target, bucket) unless target_exists
      end

      redirect_to [:account, :property, :themes], notice: 'Theme was successfully created.'
    else
      render :new
    end
  end

  def destroy
    @theme = Theme.find(params[:id])
    bucket = ENV['TEMPLATES_BUCKET']

    if @theme 
      @theme.destroy
      
      languages = index_assets("#{@account.name}/", bucket)
      languages.each do |lang|
        lang = lang.split('/').last
        template_asset = "#{@account.name}/#{lang}/#{@property.name}/templates/#{@theme.name}.handlebars"
        email_template_asset = "#{@account.name}/#{lang}/#{@property.name}/emailTemplates/#{@theme.name}/customerPaymentOptions.handlebars"
        partial_asset = "#{@account.name}/#{lang}/#{@property.name}/partials/#{@theme.name}/layout.handlebars"
        
        template_file = delete_asset_from(template_asset, bucket)
        email_template_folder = delete_asset_from(email_template_asset, bucket)
        partial_folder = delete_asset_from(partial_asset, bucket)
      end
      flash[:information] = 'Theme was removed.'
    end
    redirect_to [:account, :property, :themes]
  end

  private
    
    def load_parents
      @account = Account.find(params[:account_id])
      @property = Property.find(params[:property_id])
    end

    def theme_params
      params.require(:theme).permit([:name, :property_id])
    end
end
