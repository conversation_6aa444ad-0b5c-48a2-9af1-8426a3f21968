class UsersController < ApplicationController
  # before_action :set_user, only: [:permissions, :update_permissions]
  load_and_authorize_resource

  def index

  end

  def index_for_account
    @users = @users.where(account_id: params[:account])
    @account = Account.find(params[:account])
    render "users/index"
  end

  def new_for_account
    @account = Account.find(params[:account])
    render "users/new"
  end

  def create_for_account
    @account = Account.find(params[:account])
    user_params = params[:user].each { |key, value| value.strip! }
    camelized_params = params[:user].to_unsafe_hash.transform_keys{ |key| key.to_s.camelize(:lower)}
    camelized_params[:id] = user_params[:email]
    camelized_params.delete(:password)
    camelized_params[:photo] = {small:nil}
    camelized_params[:photo][:small] = params[:user][:photo]
    args = camelized_params.to_json

    @user = User.new({account_id: params[:account], email: user_params[:email], password: user_params[:password], password_confirmation: user_params[:password] })
    if @user.save
      languages = get_languages(@account.name.upcase)
      languages.each do |lang|
        key  = "#{lang}#{user_params[:email]}"
        body = args
        s3 = Aws::S3::Client.new(region: ENV['REGION'])
        s3.put_object(bucket: ENV['OPERATORS_BUCKET'], key: key, body: body)
      end
      redirect_to :account_users, notice: 'Operator was successfully created.'
    else
      redirect_to :account_users, alert: 'ERROR.'
    end

  end

  def permissions
    @role_types = RoleType.all
    @properties = @user.try(:account).try(:properties) || Property.all
  end

  def destroy
    set_user
    @account = @user.account
    if @user 
      languages = get_languages(@account.name.upcase)
      languages.each do |lang|
        key  = "#{lang}#{@user.email}"
        s3 = Aws::S3::Client.new(region: ENV['REGION'])
        s3.delete_object(bucket: ENV['OPERATORS_BUCKET'], key: key)
      end
      @user.destroy
      flash[:information] = 'User was removed.'
    end
    redirect_to account_users_path(@account)
  end

  def update_permissions
    @user.roles.delete_all
    permissions = params[:permissions]
    permissions.try(:each) do |p|
      property_id, role = p.split(":")
      property = Property.find(property_id)
      @user.add_role(role, property)
    end
    redirect_to user_permissions_path(@user), notice: "Updated!"
  end

  def show_otp
    resource = current_user || current_admin
    @verifier = session[:otp_token]
    if resource && !resource.otp_required_for_login
      redirect_to enable_otp_show_path
    elsif @verifier.nil?
      redirect_to new_user_session_path
    end
  end

  def verify_otp
    verifier = Rails.application.message_verifier(:otp_session)
    user_id = verifier.verify(session[:otp_token])
    user = User.find(user_id) rescue Admin.find(user_id)
    otp_attempt = params[:user][:otp_attempt]

    if user.validate_and_consume_otp!(otp_attempt)
      # OTP is correct. Log the user in
      if user.class == Admin
        sign_in(:admin, user)
      else
        sign_in(:user, user)
      end
      redirect_to authenticated_root_path, notice: 'Logged in successfully!'
    else
      flash[:alert] = 'Invalid OTP code.'
      # Send them back to the sign in page, but don't show them the OTP entry page again.
      # This is because we don't want the user to know whether the OTP code was invalid
      # because they didn't fill out the form correctly, or if the OTP code was just wrong.
      redirect_to new_user_session_path
    end
  end

  def enable_otp_show

  end

  def enable_otp_show_qr
    resource = current_user || current_admin
    if resource.otp_required_for_login
      redirect_to authenticated_root_path, alert: '2FA is already enabled.'
    else
      resource.otp_secret = User.generate_otp_secret
      issuer = 'Quotelier'
      label = "#{resource.email}"

      @provisioning_uri = resource.otp_provisioning_uri( label, issuer: issuer)
      resource.save!
    end
  end

  def enable_otp_verify
    resource = current_user || current_admin
    otp_attempt = params[:user][:otp_attempt]
    if resource.validate_and_consume_otp!(otp_attempt)
      resource.otp_required_for_login = true
      resource.save!
      redirect_to authenticated_root_path, notice: '2FA enabled successfully.'
    else
      redirect_to enable_otp_show_qr_path, alert: 'Invalid OTP code.'
    end
  end

  def export
    operators = User.all
    recs = []
    operators.each do |operator|
      account_name = operator.account.name
      bucket = ENV['OPERATORS_BUCKET']
      asset_key = "#{account_name}/en/#{operator.email}"
      asset = get_asset_from(asset_key, bucket)
      json = JSON.parse(asset)
      rec = json.to_h
      rec = rec.symbolize_keys
      # rec[:avatar] = rec[:photo]["small"]
      rec[:company] = account_name
      rec[:id] = rec[:email]
      # rec[:hash] = OpenSSL::HMAC.hexdigest('sha256', ENV['INTERCOM_HASH'], rec[:email])
      rec.slice!(:id, :company, :fullName, :email, :phoneNumber, :avatar)
      recs << rec
    end
    
    attributes = recs.first.keys
    @export = CSV.generate(headers: true) do |csv|
      csv << attributes

      recs.each do |rec|
        csv << attributes.map{ |attr| rec[attr] }
      end
    end
  end

  private

    def get_languages(account)
      s3 = Aws::S3::Client.new(region: ENV['REGION'])
      @folders = []
      begin
        resp = s3.list_objects(bucket: ENV['PROPERTIES_BUCKET'], prefix: "#{account}/", delimiter:'/')
        resp.common_prefixes.map(&:prefix)
      rescue Aws::S3::Errors::ServiceError => e
        puts e.message
      end
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_user
      @user = User.find(params[:id])
    end

    # Never trust parameters from the scary internet, only allow the white list through.
    def user_params
      params.require(:user).permit(:email)
    end
end
