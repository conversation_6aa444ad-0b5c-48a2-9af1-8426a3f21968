import {QueryAccessor} from "searchkit";

export class ConversionRateAccessor extends QueryAccessor {

  getResults() {
    const res = super.getResults();
    return res;
  }

  buildSharedQuery(query) {
    query = super.buildSharedQuery(query);

    const aggregation = {};
    aggregation[this.key] = {
        "filters": {
          "filters": {
            "all": {
              "query_string" : {
                "query": "state: *"
              }
            }
          }
        }
      };

    query = query.setAggs(aggregation);
    return query;
  }
}