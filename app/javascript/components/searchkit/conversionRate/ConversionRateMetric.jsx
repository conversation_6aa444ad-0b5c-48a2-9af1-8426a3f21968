import React from 'react';
import {SearchkitComponent} from 'searchkit';
import {ConversionRateAccessor} from './ConversionRateAccessor';


export class ConversionRateMetric extends SearchkitComponent {
  static defaultProps = {
    queryString: '*'
  };

  defineAccessor() {
    return this.accessor = new ConversionRateAccessor('conversionRate', {
      queryString: this.props.queryString
    });
  }

  getConversion() {
    const results = this.getResults();

    if (results) {
      try {
        let all = results.aggregations.conversionRate.buckets.all.doc_count;
        let current = results.hits.total;
        return (current / all) * 100;
      } catch(err) {
        return 0;
      }
    } else {
      return 0;
    }
  }

  render() {
    return (
      <div>
        <h3>Conversion rate metric:</h3>
        <p>{this.getConversion().toFixed(2)} %</p>
      </div>
    )
  }
}