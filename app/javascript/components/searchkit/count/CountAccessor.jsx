import { QueryAccessor } from "searchkit";

export class CountAccessor extends QueryAccessor {

  getResults() {
    const results = super.getResults();

    if (!results)
      return [];
    const aggs = results.aggregations[this.key];

    if(this.options.queryString) {
      return results.aggregations[this.key].buckets.all.doc_count;
    } else {
      return results.hits.total;
    }
  }

  buildSharedQuery(query) {
    query = super.buildSharedQuery(query);
    if (this.options.queryString) {
      const aggregation = {};
      aggregation[this.key] = {
        "filters": {
          "filters": {
            "all": {
              "query_string": {
                "query": this.options.queryString
              }
            }
          }
        }
      };
      return query.setAggs(aggregation);
    } else {
      return query;
    }
  }
}