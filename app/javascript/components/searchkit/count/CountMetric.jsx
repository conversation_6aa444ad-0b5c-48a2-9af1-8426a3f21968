import React from 'react';
import PropTypes from 'prop-types';
import {SearchkitComponent} from 'searchkit';
import {CountAccessor} from './CountAccessor';


export class CountMetric extends SearchkitComponent {
  static defaultProps = {
    label: 'count',
    queryString: null
  };
  static propTypes = {
    id: PropTypes.string.isRequired
  };

  defineAccessor() {
    return this.accessor = new CountAccessor(`countMetric_${this.props.id}`, {
      queryString: this.props.queryString
    });
  }

  render() {
    return (
      <div>
        <h3>{this.props.label}:</h3>
        <p>{this.accessor.getResults()}</p>
      </div>
    )
  }
}