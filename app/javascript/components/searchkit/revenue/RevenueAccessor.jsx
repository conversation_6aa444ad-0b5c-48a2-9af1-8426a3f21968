import {QueryAccessor} from "searchkit";

export class RevenueAccessor extends QueryAccessor {
  buildSharedQuery(query) {
    query = super.buildSharedQuery(query);
    const aggregation = {};
    aggregation[this.key] = {
      "sum": {
          "script": {
            "inline": [
              "int totalAcceptedOfficialRate = 0;",
              "for (int i = 0; i < params['_source']['offers'].length; ++i) {",
              "  if(params['_source']['offers'][i]['accepted'] === true) {",
              "    totalAcceptedOfficialRate+=params['_source']['offers'][i]['officialRate'];",
              "  }",
              "}",
              "return totalAcceptedOfficialRate;"
            ].join('\n'),
            "lang": "painless"
          }
        }
    };
    query = query.setAggs(aggregation);
    return query;
  }
}