import React from 'react';
import {SearchkitComponent} from 'searchkit';
import {RevenueAccessor} from './RevenueAccessor';


export class RevenueMetric extends SearchkitComponent {
  defineAccessor() {
    return this.accessor = new RevenueAccessor('revenue');
  }

  getRevenue() {
    const results = this.getResults();
    if (results) {
      try {
        return results.aggregations.revenue.value;
      } catch (err) {
        return 0;
      }
    } else {
      return 0;
    }
  }

  render() {
    return (
      <div>
        <h3>Revenue (EUR):</h3>
        <p>€ {parseInt(this.getRevenue())}</p>
      </div>
    )
  }
}