import React from 'react';
import ReactDOM from 'react-dom';
import JsonForm from './json_form';

const formElement = document.getElementById('form');
const props = JSON.parse(formElement.dataset.reactProps || '{}');
const assetElement = document.getElementById('body');
const assetFormElement = document.getElementById('asset-form');

ReactDOM.render(<JsonForm {...props} assetFormElement={assetFormElement} assetElement={assetElement} />, formElement);
