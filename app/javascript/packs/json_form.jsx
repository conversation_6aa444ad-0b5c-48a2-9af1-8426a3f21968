import React, { Component } from "react";
import Form from "react-jsonschema-form";
import fields from "react-jsonschema-form-extras";

class JsonForm extends Component {
  render() {
    const log = (type) => console.log.bind(console, type);
    const { schema, uiSchema, formData, assetElement, assetFormElement } = this.props;
    const _onChange = (object) => { assetElement.value = JSON.stringify(object.formData); };
    const _onSubmit = () => { assetFormElement.submit(); };
    return (
      <Form
        fields={fields}
        schema={schema}
        uiSchema={uiSchema}
        formData={formData}
        onChange={_onChange}
        onSubmit={_onSubmit}
      />
    );
  }
}

export default JsonForm;
