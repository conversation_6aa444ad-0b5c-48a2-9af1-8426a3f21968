import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { List } from 'semantic-ui-react';

import { formatDate } from '../../../utils/utils';

export class RequestDetails extends Component {
    static propTypes = {
        request: PropTypes.object.isRequired
    };
    render() {
        return (
            <List style={{padding: '16px'}}>
                <List.Item><List.Header>ID</List.Header>{this.props.request.id}</List.Item>
                <List.Item><List.Header>Expire</List.Header>{formatDate(this.props.request.request.expireAt)}</List.Item>
                <List.Item><List.Header>Created</List.Header>{formatDate(this.props.request.activities.created)}</List.Item>
                <List.Item><List.Header>Update</List.Header>{formatDate(this.props.request.activities.updated)}</List.Item>
            </List>
        )
    }
}