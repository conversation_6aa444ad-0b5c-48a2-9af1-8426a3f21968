import React from 'react';
import ReactTable from 'react-table';
import { SearchkitComponent, Pagination, HitsStats, SortingAccessor } from 'searchkit';

import 'react-table/react-table.css'
import {RequestDetails} from './RequestDetails';


const COLUMNS = [
  {
    Header: "Account",
    accessor: "request.accountName",
    key: "request.accountName.keyword"
  },
  {
    Header: "Property",
    accessor: "request.propertyCode",
    key: "request.propertyCode.keyword"
  },
  {
    Header: "Operator",
    accessor: "operatorId",
    key: "operatorId.keyword"
  },
  {
    Header: "Contact",
    accessor: "contact.email",
    key: "contact.email.keyword"
  },
  {
    Header: "ID",
    accessor: "id",
    key: "id.keyword"
  },
  {
    Header: "State",
    accessor: "state"
  },
]

export class RequestTable extends SearchkitComponent {
  static defaultProps = {
    columns: COLUMNS,
    pageSize: 25
  };

  defineAccessor() {
    return new SortingAccessor('sort', {
      options: this.props.columns.map((col) => {
        return {
          field: `${col.key || col.accessor}`,
          key: `${col.accessor}`,
        }
      })
    });
  }

  getPage() {
    return parseInt(this.pagination.state.getValue(), 10) - 1;
  }

  sortChange = (sort) => {
    this.accessor.state = this.accessor.state.setValue(sort[0].id);
    this.searchkit.reloadSearch();
  }

  render() {
    const results = this.getResults();

    if (!results) {
      return null;
    }
    const columns = this.props.columns;
    const data = results.hits.hits.map((item) => item._source);

    const pages = results.hits.total / this.props.pageSize;

    return (
      <div>
        <ReactTable
          ref={(r) => this.checkboxTable = r}
          showPagination={false}
          sortable={false}
          onSortedChange={this.sortChange}
          pages={pages}
          pageSize={this.props.pageSize}
          data={data}
          SubComponent={(row) => (<RequestDetails request={row.original}/>)}
          columns={columns} />
        <HitsStats />
        <Pagination showText={true} />
      </div>
    )
  }
}
