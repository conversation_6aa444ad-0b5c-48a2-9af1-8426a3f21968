import React, { Component } from 'react';
import { SortingSelector } from 'searchkit';

export class Sorting extends Component {
    render() {
        return (
            <div>
                <SortingSelector options={[
                    { label: "New Created", field: "activities.created", order: 'desc' },
                    { label: "Latest Updated", field: "activities.updated", order: 'desc' },
                    { label: "Near Expiration", field: "request.expireAt", order: 'asc' },
                ]} />
            </div>
        )
    }
}