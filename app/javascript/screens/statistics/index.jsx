import React, { Component } from 'react';

import { SearchkitManager, SearchkitProvider } from 'searchkit';
import { RequestTable, AccountFilter, Sorting, StateFilter } from './components';
import { Grid } from 'semantic-ui-react';
import Menu from 'semantic-ui-react/dist/commonjs/collections/Menu/Menu';

const sk = new SearchkitManager('/statistics', {
  timeout: 15000
});

sk.addDefaultQuery((query) => {
  return query.setSize(25)
    .setSource({
      include: [
        'id',
        'activities',
        'request',
        'operatorId',
        'contact',
        'state',
      ],
      exclude: [
        'request.property.*',
        'request.message'
      ]
    });
})

export class Dashboard extends Component {
  render() {
    return (
      <SearchkitProvider searchkit={sk}>
        <Grid padded style={{ maxHeight: "100vh", overflow: "hidden" }}>
          <Grid.Row>
            <Grid.Column width="6" style={{ overflowY: "scroll" }}>
              <Grid>
                <Grid.Row>
                  <Grid.Column width="8">
                    <AccountFilter />
                  </Grid.Column>
                  <Grid.Column width="8">
                    <StateFilter />
                  </Grid.Column>
                </Grid.Row>
              </Grid>
            </Grid.Column>
            <Grid.Column width="10" style={{ overflowY: "scroll" }}>
              <Menu>
                <Menu.Item>
                  <Sorting />
                </Menu.Item>
              </Menu>
              <RequestTable />
            </Grid.Column>
          </Grid.Row>
        </Grid>
      </SearchkitProvider>
    );
  }
}
