class Ability
  include CanCan::Ability

  def initialize(user)
    # Define abilities for the passed in user here. For example:
    #
      user ||= User.new # guest user (not logged in)

      unless user.class == Admin
        
        # if user.has_role? :manager, :any
        #   can :read, Property, :id => Property.with_role(:manager, user).pluck(:id)
        # end
        # can :manage, Property, :account_id => user.account_id
        # can :manage, User, :account_id => user.account_id
        can [:show_otp, :verify_otp], User, :id => user.id
        # can :update, User, :id => user.id
        cannot [:destroy, :export, :index, :index_for_account, :permissions, :update_permissions], User
        if user.has_any_role? :god, { name: :index, resource: :any}, { name: :edit, resource: :any}, { name: :create, resource: :any}
          can :manage, Availability, :account_id => user.account_id
          can [:show_otp, :verify_otp, :enable_otp_show, :enable_otp_show_qr, :enable_otp_verify], User, :id => user.id
        end
      else
        can :manage, :all 
      end
    #
    # The first argument to `can` is the action you are giving the user
    # permission to do.
    # If you pass :manage it will apply to every action. Other common actions
    # here are :read, :create, :update and :destroy.
    #
    # The second argument is the resource the user can perform the action on.
    # If you pass :all it will apply to every resource. Otherwise pass a Ruby
    # class of the resource.
    #
    # The third argument is an optional hash of conditions to further filter the
    # objects.
    # For example, here the user can only update published articles.
    #
    #   can :update, Article, :published => true
    #
    # See the wiki for details:
    # https://github.com/CanCanCommunity/cancancan/wiki/Defining-Abilities
  end
end
