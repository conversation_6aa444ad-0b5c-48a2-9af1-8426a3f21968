class User < ApplicationRecord
  rolify
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable and :omniauthable
  devise :recoverable, :rememberable, :trackable, :validatable
  devise :two_factor_authenticatable,
         :otp_secret_encryption_key => ENV['OTP_SECRET']

  belongs_to :account

  # Get a limited lifetime JWT with the User permissions payload
  def permissions_token
    exp = Time.now.to_i + Devise.timeout_in.to_i # time limited token
    payload = {permissions: permissions, "accountName": self.account.name, "operatorId": self.email, exp: exp}

    JWT.encode payload, ENV['HMAC'], 'HS256'
  end

  private
    def permissions
      properties  = Property.where(:account_id => self.account_id).to_a
      permissions = self.roles.map{|r| {properties.select{ |p| p.id == r.resource_id }.first.code => r.name}}
    end

end
