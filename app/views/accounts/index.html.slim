h1 Accounts
.ui.text.top.menu
  .right.item
    = link_to "Create New", create_new_account_path(:new), :class => 'ui blue button'
.ui.divided.items
  - @accounts.each do |account|
    .item
      .content
        .header
          = account.name.upcase
        .extra
          .ui.right.floated.buttons
            = link_to account_users_path(account), :class => 'ui button'
              i.users.icon
              | Operators
            = link_to account_properties_path(account), :class => 'ui button'
              i.hotel.icon
              | Properties
            = link_to account_path(account), method: :delete, data: {confirm: "Are you sure?"}, :class => 'ui button'
              i.trash.icon
              | Remove
