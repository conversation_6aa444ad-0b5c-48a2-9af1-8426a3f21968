h1 Cache #{@account.name.humanize} Assets

= form_for(:initialize_caching_update, html: {class: 'ui form'}) do |f|
  div.field
    = f.label :language
    = f.text_field :language, :value => "en"
  div.field
    = f.label :account_name
    = f.text_field :account_name, :value => @account.name
    = f.hidden_field :account_id, :value => @account.id
  div.field
    = f.label :property_code
    = f.text_field :property_code , :value => @property.name
  div.actions
    = f.submit "Cache Property Assets", class: 'ui button' 
