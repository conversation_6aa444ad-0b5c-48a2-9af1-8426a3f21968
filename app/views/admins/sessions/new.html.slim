.ui.middle.aligned.center.aligned.grid.container
  .eight.wide.computer.ten.wide.tablet.sixteen.wide.mobile.column
    h2.ui.red.header
      |  Log-in to your account
    = form_for(resource, as: resource_name, url: session_path(resource_name), html: {class: "ui large form"}) do |f|
      .ui.stacked.segment
        .field
          .ui.left.icon.input
            i.user.icon
            = f.email_field :email, autofocus: true, placeholder:"E-mail address"
        .field
          .ui.left.icon.input
            i.lock.icon
            = f.password_field :password, autocomplete: "off", placeholder:"Password"
        = f.submit "Log in", class: "ui fluid large red submit button"
      .ui.error.message