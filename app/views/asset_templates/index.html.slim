h1 Template on #{@prefix}
.ui.middle.aligned.divided.link.list
 - @folders.each do |folder|
  a.item href=asset_templates_path(folder)
    i.folder.icon
    .content
      .header
        - folder.slice! @prefix
        = folder
.ui.middle.aligned.divided.link.list
 - @files.each do |file|
  a.item href=asset_template_path("#{@prefix}#{file[:key]}")
    .right.floated.content
      = time_ago_in_words(file[:last_modified])
    i.file.icon
    .content
      .description
        = file[:key]

.ui.middle.aligned.divided.link.list
 - @default_files.each do |default_file|
  - filename = default_file[:key]
  a.item href=asset_template_copy_path("#{@default_path}#{filename}", "#{@prefix}#{filename}")
    .right.floated.content
      = time_ago_in_words(default_file[:last_modified])
    i.file.green.icon
    .content
      .description
        = default_file[:key]
