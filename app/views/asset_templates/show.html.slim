h1 Template on #{@item}
= form_for(:asset_template, html: {class: 'ui form'}) do |f|
  div.field
    input type="hidden" name="item" value=@item
    textarea name="body" style="display:none;"
      = @asset
  div.actions
    = f.submit "Update", class: 'ui button'
    = link_to "Preview", asset_template_preview_url(@item, exclude: true, :protocol => "http"), target: '_blank', :class => 'ui right floated blue button' unless @template_type != 'templates'
    = link_to "DELETE", asset_template_destroy_url(@item), method: :delete, data: {confirm: "Are you sure?"}, :class => 'ui right floated red button'

pre#ace-editor class="#{@template_type}"
  = @asset