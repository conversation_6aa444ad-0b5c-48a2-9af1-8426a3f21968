h1 Assets of Properties on #{@prefix}
.ui.middle.aligned.divided.link.list
 - @folders.each do |folder|
  a.item href=assets_path(folder)
    i.folder.icon
    .content
      .header
        - folder.slice! @prefix
        = folder
.ui.middle.aligned.divided.link.list
 - @files.each do |file|
  a.item href=asset_path(file.key)
    .right.floated.content
      = time_ago_in_words file.last_modified
    i.file.icon
    .content
      .description
        - file.key.slice! @prefix
        = file.key