h1 Assets of Operators on #{@prefix}
.ui.middle.aligned.divided.link.list
 - @folders.each do |folder|
  a.item href=asset_operators_path(folder)
    i.folder.icon
    .content
      .header
        - folder.slice! @prefix
        = folder
.ui.middle.aligned.divided.link.list
 - @files.each do |file|
  a.item href=asset_operator_path(file.key)
    .right.floated.content
      = time_ago_in_words file.last_modified
    i.file.icon
    .content
      .description
        - file.key.slice! @prefix
        = file.key