h1 Asset of Operator on #{@item}

= form_for(:asset, url: asset_operator_path(@item), html: {class: 'ui form', id: 'asset-form'}) do |f|
  div.field
    input type="hidden" name="item" value=@item
    textarea#body name="body" class="ui hidden"
      = @asset
  div.actions
    = link_to "DELETE", destroy_asset_operators_url(@item), method: :delete, data: {confirm: "Are you sure?"}, :class => 'ui right floated red button'

= react_component 'AssetForm', @props, {id: "form"}