.ui.inverted.menu class=(admin_signed_in? ? 'red' : 'blue')
  .ui.container
    a.header.item[href="/"]
      |  Quotelier
      - if admin_signed_in?
        = link_to "Accounts", accounts_path, :class => 'item'
        = link_to "Assets", assets_path, :class => 'item'
        = link_to "Operators", asset_operators_path, :class => 'item'
        = link_to "Templates", asset_templates_path, :class => 'item'
        = link_to "Upload", remote_files_path, :class => 'item'
        = link_to "Flags", role_types_path, :class => 'item'
        = link_to "Stats", statistics_path, :class => 'item'
    div.right.menu
      - if admin_signed_in? || user_signed_in?
        = link_to "Logout", destroy_admin_session_path, :class => 'item'
      - else
        = link_to "Login", new_user_session_path, :class => 'item'
