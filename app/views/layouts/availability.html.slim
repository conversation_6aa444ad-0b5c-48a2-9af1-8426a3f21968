doctype html
html
  head
    meta content=("text/html; charset=UTF-8") http-equiv="Content-Type" /
    title Quotelier
    = csrf_meta_tags
    = stylesheet_link_tag    'application', media: 'all', 'data-turbolinks-track': 'reload'
    = javascript_include_tag 'application', 'data-turbolinks-track': 'reload'
    = stylesheet_link_tag "#{ENV['REACTAPP']}/js/app.css"

    = javascript_include_tag "#{ENV['REWUNDO_HOST']}/main.js"
    javascript:
      window.rt = window.rt || function() { (rt.q = rt.q || []).push(arguments) };
      rewundo.rt('#{ENV["REWUNDO_APIKEY"]}', '#{@props[:operatorId]}');
  body
    = yield
    = javascript_include_tag "#{ENV['REACTAPP']}/js/app.js"