doctype html
html
  head
    meta content=("text/html; charset=UTF-8") http-equiv="Content-Type" /
    title Quotelier
    link href="#{ENV['REACTAPP']}/apple-touch-icon.png" rel="apple-touch-icon" sizes="76x76"
    link href="#{ENV['REACTAPP']}/favicon-32x32.png" rel="icon" type="image/png" sizes="32x32"
    link href="#{ENV['REACTAPP']}/favicon-16x16.png" rel="icon" type="image/png" sizes="16x16"
    link href="#{ENV['REACTAPP']}/manifest.json" rel="manifest"
    link href="#{ENV['REACTAPP']}/safari-pinned-tab.svg" rel="mask-icon" color="#5bbad5"
    link href="#{ENV['REACTAPP']}/safari-pinned-tab.svg" rel="apple-touch-startup-image"
    meta name="theme-color" content="#ffffff"
    meta name="apple-mobile-web-app-capable" content="yes"
    meta name="apple-mobile-web-app-status-bar-style" content="white"
    meta name="mobile-web-app-capable" content="yes"
    meta name="viewport" content="width=device-width"
    = csrf_meta_tags
    = stylesheet_link_tag    'application', media: 'all', 'data-turbolinks-track': 'reload'
    = javascript_include_tag 'application', 'data-turbolinks-track': 'reload'
  body
    div.ui.container
      = semantic_flash
      = yield
