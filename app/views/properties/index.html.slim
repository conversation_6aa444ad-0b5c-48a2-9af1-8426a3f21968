h1
  | Properties of 
  = @account.name
.ui.text.top.menu
  .right.item
    = link_to "Create Properties", new_account_property_path(@account.name), :class => "ui blue button"
div.ui.divided.items
  - @properties.each do |property|
    div.item
      div.content
        .header 
          = property.name
        .extra
          .ui.right.floated.buttons
            = link_to account_property_themes_path(@account,property), :class => 'ui button'
              i.file.icon
              | Themes
            = link_to initialize_caching_path(@account, property.name), :class => 'ui button'
              i.save.icon
              | Initialize Assets
            = link_to initialize_property_upsales_path(@account.name, @account.id, property.name), :class => 'ui button'
              i.save.icon
              | Initialize Upsales
            = link_to clear_property_cache_path(@account.name, @account.id, property.name), :class => 'ui button'
              i.eraser.icon
              | Clear Cache
            = link_to account_property_path(id: property.id), method: :delete, :class => 'ui button'
              i.trash.icon
              | Remove
