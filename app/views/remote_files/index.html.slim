h1 Files of Properties on #{@prefix}
.ui.text.top.menu
  .right.item
    = link_to "UPLOAD", new_remote_file_path(), :class => 'ui blue button'    
.ui.middle.aligned.divided.link.list
 - @folders.each do |folder|
  a.item href=remote_files_path(folder)
    i.folder.big.icon
    .content
      .header
        - folder.slice! @prefix
        = folder
.ui.middle.aligned.divided.link.list
 - @files.each do |file|
  .item href="http://files.quotelier.net/#{file.key}" target='_blank'
    .right.floated.content
      = link_to 'DELETE', destroy_remote_file_path(file.key), method: :delete, data: {confirm: "Are you sure?"}, :class => 'ui tiny red button'
    .left.floated.content
      a.icon href="http://files.quotelier.net/#{file.key}" target='_blank'
        i.file.big.link.icon
    .content
      .header
        - file.key.slice! @prefix
        = file.key
      .description
      = time_ago_in_words file.last_modified