h1 Flags
.ui.text.top.menu
  .right.item
    = link_to "Create Flag", new_role_type_path, :class => "ui blue button"
div.ui.divided.items
  - @role_types.each do |role_type|
    div.item
      div.content
        .header 
          = role_type.name
        .extra
          .ui.right.floated.buttons
            = link_to assign_to_all_path(role_type.name), :class => 'ui button'
              i.checkmark.icon
              | assign to all
            = link_to role_type_path(role_type), method: :delete, :class => 'ui button'
              i.trash.icon
              | Remove
