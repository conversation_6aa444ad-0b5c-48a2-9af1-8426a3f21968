h1 
  | Themes of 
  = @property.name
.ui.text.top.menu
  .right.item
    = link_to "Create New", new_account_property_theme_path(@account, @property), :class => 'ui blue button'
.ui.divided.items
  - @themes.each do |theme|
    .item
      .content
        .header
          = theme.name
        .extra
          .ui.right.floated.buttons
            = link_to account_property_theme_path(@account, @property,theme), method: :delete, data: {confirm: "Are you sure?"}, :class => 'ui button'
              i.trash.icon
              | Remove
