.ui.middle.aligned.center.aligned.grid.container
  .eight.wide.computer.ten.wide.tablet.sixteen.wide.mobile.column
    - if admin_signed_in? || user_signed_in?
      h2 Enable Two Factor Authentication
      div 
        = raw RQRCode::QRCode.new(@provisioning_uri).as_svg(module_size: 5)
      h3 Scan the QR Code 
      
    = form_for(:user, url: enable_otp_verify_path, html: {method: :post, class: "ui large form"}) do |f|
      .ui.stacked.segment
        .field
          .ui.left.icon.input
            i.lock.icon
            = f.text_field :otp_attempt, autofocus: true, autocomplete: "off", placeholder: "OTP Code", required: true
        = f.submit "Verify and Enable", class: "ui fluid large blue submit button"
      .ui.error.message
        / = devise_error_messages!
    .ui.message
      / = render "users/shared/links"
