h1 New Operator for #{@account.name.upcase}

= form_for(:user, html: {class: 'ui form'}) do |f|
  div.field
    = f.label :email
    = f.text_field :email
  div.field
    = f.label :password
    = f.password_field :password, autocomplete: "off"
  div.field
    = f.label :fullName
    = f.text_field :fullName
  div.field
    = f.label :nickName
    = f.text_field :nickName
  div.field
    = f.label :title
    = f.text_field :title
  div.field
    = f.label :description
    = f.text_field :description
  div.field
    = f.label :phoneNumber
    = f.text_field :phoneNumber
  div.field
    = f.label :photo
    = f.text_field :photo
  div.actions
    = f.submit "Create New Operator", class: 'ui button' 