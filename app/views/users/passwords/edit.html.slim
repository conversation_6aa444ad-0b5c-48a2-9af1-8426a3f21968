.ui.middle.aligned.center.aligned.grid.container
  .eight.wide.computer.ten.wide.tablet.sixteen.wide.mobile.column
    .ui.image
      img src="https://s3-eu-west-1.amazonaws.com/quotelier/assets/images/Quotelier-Logo.png"
    = form_for(resource, as: resource_name, url: password_path(resource_name), html: {method: :put, class: "ui large form #{if resource.errors.any? then :error end}"}) do |f|
      = f.hidden_field :reset_password_token
      .ui.stacked.segment
        .field
          .ui.left.icon.input
            i.lock.icon
            = f.password_field :password, autofocus: true, autocomplete: "off", placeholder: "New password"
        .field
          .ui.left.icon.input
            i.lock.icon
            = f.password_field :password_confirmation, autocomplete: "off", placeholder: "Confirm new password"
        = f.submit "Change my password", class: "ui fluid large blue submit button"
      .ui.error.message
        = devise_error_messages!
    .ui.message
      = render "users/shared/links"
