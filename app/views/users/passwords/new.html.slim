.ui.middle.aligned.center.aligned.grid.container
  .eight.wide.computer.ten.wide.tablet.sixteen.wide.mobile.column
    .ui.image
      img src="https://s3-eu-west-1.amazonaws.com/quotelier/assets/images/Quotelier-Logo.png"
    = form_for(resource, as: resource_name, url: password_path(resource_name), html: {class: "ui large form #{if resource.errors.any? then :error end}"}) do |f|
      .ui.stacked.segment
        .field
          .ui.left.icon.input
            i.user.icon
            = f.email_field :email, autofocus: true, placeholder:"E-mail address"
        = f.submit "Send me reset password instructions", class: "ui fluid large blue submit button"
      .ui.error.message
        = devise_error_messages!
    .ui.message
      = render "users/shared/links"