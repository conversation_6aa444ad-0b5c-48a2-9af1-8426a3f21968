h1 = @user.email
= form_tag "permissions/update", class: "ui form", remote: true
  table.ui.celled.table
    thead
      tr
        th = "Property / Permisions"
        - @role_types.each do |role|
          th = role.name.capitalize
    tbody
      - @properties.each do |property|
        tr
          td = property.code
          - @role_types.each do |role|
            td
              div.field
                div.ui.fitted.toggle.checkbox
                  = check_box_tag "permissions[]", "#{property.id}:#{role.name}", @user.has_role?(role.name, property)
                  = label_tag
  = submit_tag "Update", class: "ui primary button right floated"
  = link_to "DELETE", user_destroy_path(@user), method: :delete, class: "ui danger button"