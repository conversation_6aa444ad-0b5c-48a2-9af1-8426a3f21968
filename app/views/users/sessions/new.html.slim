.ui.middle.aligned.center.aligned.grid.container
  .eight.wide.computer.ten.wide.tablet.sixteen.wide.mobile.column
    .ui.image
      img src="https://s3-eu-west-1.amazonaws.com/quotelier/assets/images/Quotelier-Logo.png"
    = form_for(resource, as: resource_name, url: session_path(resource_name), html: {class: "ui large form"}) do |f|
      .ui.stacked.segment
        .field
          .ui.left.icon.input
            i.user.icon
            = f.email_field :email, autofocus: true, placeholder:"E-mail address"
        .field
          .ui.left.icon.input
            i.lock.icon
            = f.password_field :password, autocomplete: "off", placeholder:"Password"
        = f.submit "Log in to your account", class: "ui fluid large teal submit button"
      .ui.error.message
    .ui.message
      = render "users/shared/links"