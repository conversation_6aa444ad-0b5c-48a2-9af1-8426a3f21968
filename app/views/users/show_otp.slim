.ui.middle.aligned.center.aligned.grid.container
  .eight.wide.computer.ten.wide.tablet.sixteen.wide.mobile.column
    = form_for(:user, url: verify_user_otp_path, html: {method: :post, class: "ui large form"}) do |f|
      .ui.stacked.segment
        .field
          .ui.left.icon.input
            i.lock.icon
            = f.text_field :otp_attempt, autofocus: true, autocomplete: "off", placeholder: "OTP Code", required: true
        = f.submit "Verify and Enable", class: "ui fluid large blue submit button"
      .ui.error.message
        / = devise_error_messages!
    .ui.message
      / = render "users/shared/links"
