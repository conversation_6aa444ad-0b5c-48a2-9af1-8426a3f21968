require_relative 'boot'

require 'rails/all'
require 'graphql/client'
require 'graphql/client/http'
require 'elasticsearch/transport'
require 'csv'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Quotelier
  class Application < Rails::Application
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.
  end
  # Configure GraphQL endpoint using the basic HTTP network adapter.
  HTTP = GraphQL::Client::HTTP.new(ENV['GRAPHQL_ENDPOINT']) do
    def headers(context)
      token = context[:permissions_token] || ENV['QUOTELIER_TOKEN']

      {
        "Authorization" => "Bearer #{token}"
      }
    end
  end

  # Fetch latest schema on init, this will make a network request
  Schema = GraphQL::Client.load_schema(HTTP)
  Client = GraphQL::Client.new(schema: Schema, execute: HTTP)
end

Raven.configure do |config|
  config.dsn = 'https://d622361ba87b4cccb400c6f55ce0a7e9:<EMAIL>/161089'
  config.environments = %w[ production ]
end