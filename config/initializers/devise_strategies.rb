Warden::Strategies.add(:password_authenticatable) do
  def valid?
    (params['user'] && params['user']['email'] && params['user']['password']) ||
    (params['admin'] && params['admin']['email'] && params['admin']['password'])
  end

  def authenticate!
    if params['user']
      resource = User.find_by(email: params['user']['email'])
      password = params['user']['password']
    elsif params['admin']
      resource = Admin.find_by(email: params['admin']['email'])
      password = params['admin']['password']
    end

    if resource && resource.valid_password?(password)
      success!(resource)
    else
      fail!('Invalid email or password.')
    end
  end
end