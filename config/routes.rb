Rails.application.routes.draw do
  get 'assets/(:prefix)', to: 'assets#index', as: "assets"
  get 'asset/(:item)', to: 'assets#show', constraints: { item: /[^\/]+/ }, as: "asset"
  post 'asset/(:item)', to: 'assets#update', constraints: { item: /[^\/]+/ }, as: "update_asset"
  get 'assets_operators/(:prefix)', to: 'assets_operators#index', as: "asset_operators"
  get 'asset_operator/(:item)', to: 'assets_operators#show', constraints: { item: /[^\/]+/ }, as: "asset_operator"
  post 'asset_operator/(:item)', to: 'assets_operators#update', constraints: { item: /[^\/]+/ }, as: "update_asset_operators"
  delete 'asset_operator/(:item)', to: 'assets_operators#destroy', constraints: { item: /[^\/]+/ }, as: "destroy_asset_operators"
  get 'asset_templates/(:prefix)', to: 'asset_templates#index', as: "asset_templates"
  get 'asset_template/(:item)', to: 'asset_templates#show', constraints: { item: /[^\/]+/ }, as: "asset_template"
  post 'asset_template/(:item)', to: 'asset_templates#update', as: "update_asset_template"
  get 'asset_template/preview/(:item)', to: 'asset_templates#preview', as: "asset_template_preview"
  delete 'asset_template/destroy/(:item)', to: 'asset_templates#destroy', constraints: { item: /[^\/]+/ }, as: "asset_template_destroy"
  get 'asset_template/copy/(:source)/(:target)', to: 'asset_templates#copy', constraints: { source: /[^\/]+/, target: /[^\/]+/ }, as: "asset_template_copy"
  get 'accounts', to: 'accounts#index'
  post 'accounts', to: 'accounts#create'
  get 'accounts/:account_name', to: 'properties#new', as: "create_new_account"
  get 'accounts/:account_name/properties/new', to: 'properties#new', as: "new_account_property"
  get 'accounts/:account/users', to: 'users#index_for_account', as: "account_users"
  post 'accounts/:account/users/new', to: 'users#create_for_account', as: "create_account_user"
  get 'accounts/:account/users/new', to: 'users#new_for_account', as: "new_account_user"
  get 'accounts/:account_id/initialize/(:property_code)', to: 'accounts#initialize_caching', as: 'initialize_caching'
  post 'accounts/:account_id/initialize', to: 'accounts#initialize_caching_update', as: 'initialize_caching_update'
  get 'accounts/:account_id/initializeUpsales', to: 'accounts#initialize_upsales', as: 'initialize_upsales'
  get 'accounts/:user/availability', to: 'availability#index', as: 'availability'
  delete 'accounts/:account', to: 'accounts#destroy', as: 'account'

  post 'accounts/:account_name/properties/create_initialize', to: 'properties#create_initialize', as: 'create_property'
  get 'accounts/:account_name/:account_id/:property_code/initialize_upsales', to: 'properties#initialize_upsales', as: 'initialize_property_upsales'
  get 'accounts/:account_name/:account_id/:property_code/clear_property_cache', to: 'properties#clear_cache', as: 'clear_property_cache'

  get 'role_types/:role/assign_to_all', to: 'role_types#assign_role_to_all_users', as: 'assign_to_all'

  get 'files/(:prefix)', to: 'remote_files#index', as: 'remote_files'
  get 'file/new', to:'remote_files#new', as: 'new_remote_file'
  post 'files', to: 'remote_files#create'
  delete 'file/(:item)', to: 'remote_files#destroy', constraints: { item: /[^\/]+/ }, as: 'destroy_remote_file'

  get 'statistics', to: 'statistics#show', as: 'statistics'
  post 'statistics/_search', to: 'statistics#search'
  
  get 'users', to: 'users#index'
  get 'users/export', to: 'users#export'
  delete 'users/:id/destroy', to: 'users#destroy', as: "user_destroy"

  get 'users/:id/permissions', to: "users#permissions", as: "user_permissions"
  post 'users/:id/permissions/update', to: "users#update_permissions", as: "user_permissions_update"

  get 'enable_otp_show', to: 'users#enable_otp_show', as: 'enable_otp_show'
  get 'enable_otp_show_qr', to: 'users#enable_otp_show_qr', as: 'enable_otp_show_qr'
  post 'enable_otp_verify', to: 'users#enable_otp_verify', as: 'enable_otp_verify'

  get 'users/otp', to: 'users#show_otp', as: 'user_otp'
  post 'users/otp', to: 'users#verify_otp', as: 'verify_user_otp'
  # post 'verify_otp', to: 'users/sessions#verify_otp' 
  

  devise_for :users, controllers: { sessions: 'users/sessions' }
  devise_for :admins, controllers: { sessions: 'users/sessions' }

  devise_scope :user do
    authenticated :user do
      root 'availability#index', as: :authenticated_root
    end

    authenticated :admin do
      root 'accounts#index', as: :authenticated_admin_root
    end

    unauthenticated do
      root 'devise/sessions#new', as: :unauthenticated_root
    end
  end

  # For details on the DSL available within this file, see http://guides.rubyonrails.org/routing.html
  # root to: "availability#index"
  resources :accounts do
    resources :properties do
      resources :themes
    end
  end

  resources :role_types
end
