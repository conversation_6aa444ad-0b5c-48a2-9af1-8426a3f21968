services:
  db:
    image: postgres:15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: quotelier_development
    volumes:
      - ./data:/var/lib/postgresql/data

  web:
    build:
      context: .
      dockerfile: Dockerfile
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails s -p 3000 -b '0.0.0.0'"
    volumes:
      - .:/app
    working_dir: /app
    ports:
      - 3000:3000
    depends_on:
      - db
    environment:
      DB_HOST: db
      DB_USER: postgres
      DB_PASSWORD: password
      DB_NAME: quotelier_development
      AWS_ACCESS_KEY_ID: ********************
      AWS_SECRET_ACCESS_KEY: SG9xFPBBckXC6b8ETMC6ML3MaUUo2rgxGu024Z8P
      HMAC: my$$ecretK3y
      OTP_SECRET: q4r5s6t7u8v9w0x1y2z3a4b5c6d7e8f9
      LANG: C.UTF-8
      RACK_ENV: development
      RAILS_ENV: development
      RAILS_LOG_TO_STDOUT: enabled
      RAILS_SERVE_STATIC_FILES: enabled
      REACTAPP: //sta-assets.quotelier.net
      REGION: eu-central-1
      SECRET_KEY_BASE: a0e8930e0b8357817dfdfaf2fa103ec6675d64da91182ae677edd6015434f910d0dcbc5c73335bc544f1d3500df5344be5ecc6c4613461ccefe616a59a60e756
      QUOTELIER_TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************.IAfllY-d_67S_CvBAeEAgEWRP-LEzdTDIK2tcdSQvMg
      GRAPHQL_ENDPOINT: https://api.quotelier.net/stage/graphql
      STAGE: stage
      CACHE_BUCKET: integrations-stage-cache
      FILES_BUCKET: files.quotelier.net 
      OPERATORS_BUCKET: operators-service-stage-operators 
      TEMPLATES_BUCKET: properties-service-stage-templates 
      PROPERTIES_BUCKET: properties-service-stage-properties 
      INITIALIZE_ACCOUNT_FUNCTION: integrations-stage-createAccount 
      INITIALIZE_PROPERTY_FUNCTION: properties-service-stage-initializeProperty 
      INITIALIZE_UPSALE_FUNCTION: upsales-stage-initialize 
      CLEAR_CACHE_FUNCTION: integrations-stage-clearCache 
      ES_ENDPOINT: https://search-elasticsearch-quote-indexing-j6vs7kqq6lssesys7hqgrrqdoi.eu-central-1.es.amazonaws.com/ 
      ES_ACCESS_KEY_ID: ******************** 
      ES_SECRET_ACCESS_KEY: VhNxHKcZlVbFc8JNK1FRdV2T2HJfvQpYxMPHpQa7 
      INTERCOM_HASH: s0F5EyZS2ulfgFOc66KnuyCe5k4lvTCicrBqX-Aw 
      REWUNDO_HOST: https://dev.rewundo.net 
      REWUNDO_APIKEY: a0b1c2d3e4 