defmodule Qx.Accounts.Changes.AssignDefaultRole do
  @moduledoc """
  Change that assigns the default "operator" role to new users.
  
  This change ensures that all new users get the default operator role
  when they are created, providing them with basic access to the system.
  """
  
  use Ash.Resource.Change

  def change(changeset, _opts, _context) do
    case changeset.action_type do
      :create ->
        # Only assign default role on user creation
        current_roles = Ash.Changeset.get_attribute(changeset, :roles) || []
        
        # Add :operator to roles if not already present
        new_roles = 
          if :operator in current_roles do
            current_roles
          else
            [:operator | current_roles]
          end
        
        Ash.Changeset.change_attribute(changeset, :roles, new_roles)
      
      _ ->
        # Don't modify roles for other action types
        changeset
    end
  end
end
