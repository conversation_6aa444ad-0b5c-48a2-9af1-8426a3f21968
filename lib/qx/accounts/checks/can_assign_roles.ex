defmodule Qx.Accounts.Checks.CanAssignRoles do
  @moduledoc """
  Policy check that determines if an actor can assign roles for a specific property.
  
  An actor can assign roles if they have either:
  - The "assign" role type on the property
  - The "own" role type on the property (owners can assign roles)
  """
  
  use Ash.Policy.SimpleCheck

  def match?(actor, context, _opts) do
    case get_property_id(context) do
      nil -> false
      property_id -> has_assign_permission?(actor, property_id)
    end
  end

  def describe(_opts) do
    "actor can assign roles for this property"
  end

  defp get_property_id(%{changeset: %{attributes: %{property_id: property_id}}}), do: property_id
  defp get_property_id(%{resource: %{property_id: property_id}}), do: property_id
  defp get_property_id(_), do: nil

  defp has_assign_permission?(actor, property_id) do
    case actor do
      %{id: actor_id} ->
        # Query for roles where the actor has "assign" or "own" permission on this property
        query = """
        SELECT COUNT(*) 
        FROM roles r
        JOIN role_types rt ON r.role_type_id = rt.id
        WHERE r.user_id = $1 
          AND r.property_id = $2 
          AND rt.name IN ('assign', 'own')
        """
        
        case Qx.Repo.query(query, [actor_id, property_id]) do
          {:ok, %{rows: [[count]]}} when count > 0 -> true
          _ -> false
        end
      _ -> false
    end
  end
end
