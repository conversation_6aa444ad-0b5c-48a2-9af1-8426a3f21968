defmodule Qx.Accounts.Checks.HasPropertyRole do
  @moduledoc """
  Policy check that determines if an actor has specific role types on a property.
  
  Usage:
    authorize_if {HasPropertyRole, [roles: [:index, :edit]]}
    authorize_if {HasPropertyRole, [roles: [:own]]}
  """
  
  use Ash.Policy.SimpleCheck

  def match?(actor, context, opts) do
    required_roles = opts[:roles] || []
    
    case {get_actor_id(actor), get_property_id(context)} do
      {actor_id, property_id} when not is_nil(actor_id) and not is_nil(property_id) ->
        has_any_role?(actor_id, property_id, required_roles)
      _ -> false
    end
  end

  def describe(opts) do
    roles = opts[:roles] || []
    "actor has any of these roles on the property: #{inspect(roles)}"
  end

  defp get_actor_id(%{id: id}), do: id
  defp get_actor_id(_), do: nil

  defp get_property_id(%{resource: %{id: id}}), do: id
  defp get_property_id(%{resource: %{property_id: id}}), do: id
  defp get_property_id(%{changeset: %{data: %{id: id}}}), do: id
  defp get_property_id(%{changeset: %{data: %{property_id: id}}}), do: id
  defp get_property_id(%{changeset: %{attributes: %{property_id: id}}}), do: id
  defp get_property_id(_), do: nil

  defp has_any_role?(actor_id, property_id, required_roles) when is_list(required_roles) do
    case required_roles do
      [] -> false
      roles ->
        # Convert atoms to strings for database query
        role_names = Enum.map(roles, &to_string/1)
        
        query = """
        SELECT COUNT(*) 
        FROM roles r
        JOIN role_types rt ON r.role_type_id = rt.id
        WHERE r.user_id = $1 
          AND r.property_id = $2 
          AND rt.name = ANY($3)
        """
        
        case Qx.Repo.query(query, [actor_id, property_id, role_names]) do
          {:ok, %{rows: [[count]]}} when count > 0 -> true
          _ -> false
        end
    end
  end
  
  defp has_any_role?(_actor_id, _property_id, _required_roles), do: false
end
