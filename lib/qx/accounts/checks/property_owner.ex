defmodule Qx.Accounts.Checks.PropertyOwner do
  @moduledoc """
  Policy check that determines if an actor owns a specific property.
  
  An actor owns a property if they have the "own" role type on that property.
  This is a convenience check that wraps HasPropertyRole with the "own" role.
  """
  
  use Ash.Policy.SimpleCheck

  def match?(actor, context, _opts) do
    Qx.Accounts.Checks.HasPropertyRole.match?(actor, context, [roles: [:own]])
  end

  def describe(_opts) do
    "actor owns this property"
  end
end
