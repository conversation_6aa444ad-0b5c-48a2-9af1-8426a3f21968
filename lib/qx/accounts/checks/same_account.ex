defmodule Qx.Accounts.Checks.SameAccount do
  @moduledoc """
  Policy check that ensures the actor and the resource belong to the same account.
  
  This check is used to enforce account-level isolation, ensuring users can only
  access resources within their own account.
  """
  
  use Ash.Policy.SimpleCheck

  def match?(actor, context, _opts) do
    case {get_actor_account_id(actor), get_resource_account_id(context)} do
      {actor_account_id, resource_account_id} when not is_nil(actor_account_id) and not is_nil(resource_account_id) ->
        actor_account_id == resource_account_id
      _ -> false
    end
  end

  def describe(_opts) do
    "actor and resource belong to the same account"
  end

  defp get_actor_account_id(%{account_id: account_id}), do: account_id
  defp get_actor_account_id(_), do: nil

  defp get_resource_account_id(%{resource: %{account_id: account_id}}), do: account_id
  defp get_resource_account_id(%{changeset: %{data: %{account_id: account_id}}}), do: account_id
  defp get_resource_account_id(%{changeset: %{attributes: %{account_id: account_id}}}), do: account_id
  defp get_resource_account_id(_), do: nil
end
