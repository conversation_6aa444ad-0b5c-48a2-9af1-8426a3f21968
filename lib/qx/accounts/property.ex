defmodule Qx.Accounts.Property do
  use Ash.Resource,
    otp_app: :qx,
    domain: Qx.Accounts,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshRbac]

  postgres do
    table "properties"
    repo Qx.Repo
  end

  rbac do
    # Super admins can manage all properties
    bypass :super_admin

    # Operators can read properties in their account
    role :operator do
      fields([:id, :title, :name, :account_id])
      actions([:read])
    end
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [:title, :name, :account_id]
    end

    update :update do
      primary? true
      accept [:title, :name]
      require_atomic? false
    end

    destroy :destroy do
      primary? true
    end

    read :list_by_account do
      argument :account_id, :uuid, allow_nil?: false
      filter expr(account_id == ^arg(:account_id))
    end
  end

  policies do
    # Property-scoped access control using custom checks
    policy action_type(:read) do
      authorize_if Qx.Accounts.Checks.SameAccount
      authorize_if {Qx.Accounts.Checks.HasPropertyRole, [roles: [:index]]}
    end

    policy action_type(:update) do
      authorize_if Qx.Accounts.Checks.SameAccount
      authorize_if {Qx.Accounts.Checks.HasPropertyRole, [roles: [:edit]]}
    end

    policy action_type(:create) do
      authorize_if Qx.Accounts.Checks.SameAccount
      authorize_if {Qx.Accounts.Checks.HasPropertyRole, [roles: [:create]]}
    end

    policy action_type(:destroy) do
      authorize_if Qx.Accounts.Checks.SameAccount
      authorize_if {Qx.Accounts.Checks.HasPropertyRole, [roles: [:delete]]}
    end
  end

  changes do
    change Qx.Accounts.Changes.NormalizeName
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
    end

    attribute :name, :ci_string do
      allow_nil? false
      public? true
    end

    timestamps()
  end

  relationships do
    belongs_to :account, Qx.Accounts.Account do
      allow_nil? false
      public? true
    end

    has_many :property_roles, Qx.Accounts.Role do
      destination_attribute :property_id
      public? true
    end
  end
end
