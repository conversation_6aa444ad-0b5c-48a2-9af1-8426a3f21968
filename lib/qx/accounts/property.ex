defmodule Qx.Accounts.Property do
  use Ash.Resource, otp_app: :qx, domain: Qx.Accounts, data_layer: AshPostgres.DataLayer

  postgres do
    table "properties"
    repo Qx.Repo
  end

  actions do
    defaults [:read]
  end

  changes do
    change Qx.Accounts.Changes.NormalizeName
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :title, :string do
      allow_nil? false
      public? true
    end

    attribute :name, :ci_string do
      allow_nil? false
      public? true
    end

    timestamps()
  end
end
