defmodule Qx.Accounts.Role do
  use Ash.Resource,
    otp_app: :qx,
    domain: Qx.Accounts,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshRbac]

  postgres do
    table "roles"
    repo Qx.Repo
  end

  rbac do
    # Super admins can manage all roles
    bypass :super_admin
    
    # Operators can read their own roles
    role :operator do
      fields [:id, :user_id, :property_id, :role_type_id, :granted_at]
      actions [:read]
    end
  end

  policies do
    # Users can read their own roles
    policy action_type(:read) do
      authorize_if expr(user_id == ^actor(:id))
    end

    # Only users with "assign" permission on the property can create/destroy roles for that property
    policy action_type([:create, :destroy]) do
      authorize_if Qx.Accounts.Checks.CanAssignRoles
    end

    # No one can update roles - they must be destroyed and recreated
    policy action_type(:update) do
      forbid_if always()
    end
  end

  actions do
    defaults [:read]

    create :assign_role do
      primary? true
      accept [:user_id, :property_id, :role_type_id]
      
      change set_attribute(:granted_at, &DateTime.utc_now/0)
      change set_attribute(:granted_by_id, actor(:id))
      
      validate present([:user_id, :property_id, :role_type_id])
    end

    destroy :revoke_role do
      primary? true
    end

    read :for_user do
      argument :user_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id))
    end

    read :for_property do
      argument :property_id, :uuid, allow_nil?: false
      filter expr(property_id == ^arg(:property_id))
    end

    read :for_user_and_property do
      argument :user_id, :uuid, allow_nil?: false
      argument :property_id, :uuid, allow_nil?: false
      filter expr(user_id == ^arg(:user_id) and property_id == ^arg(:property_id))
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :granted_at, :utc_datetime do
      allow_nil? false
      public? true
      default &DateTime.utc_now/0
    end

    timestamps()
  end

  relationships do
    belongs_to :user, Qx.Accounts.User do
      allow_nil? false
      public? true
    end

    belongs_to :property, Qx.Accounts.Property do
      allow_nil? false
      public? true
    end

    belongs_to :role_type, Qx.Accounts.RoleType do
      allow_nil? false
      public? true
    end

    belongs_to :granted_by, Qx.Accounts.User do
      allow_nil? true
      public? true
    end
  end

  identities do
    # Ensure a user can only have one role of each type per property
    identity :unique_user_property_role_type, [:user_id, :property_id, :role_type_id]
  end
end
