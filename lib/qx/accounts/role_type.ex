defmodule Qx.Accounts.RoleType do
  use Ash.Resource,
    otp_app: :qx,
    domain: Qx.Accounts,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshRbac]

  postgres do
    table "role_types"
    repo Qx.Repo
  end

  rbac do
    # Super admins can manage role types
    bypass :super_admin

    # All authenticated users can read role types (to see available permissions)
    role :operator do
      fields([:id, :name, :description])
      actions([:read])
    end
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [:name, :description]
      validate present([:name])
      validate one_of(:name, ["index", "edit", "create", "assign", "delete", "own"])
    end

    update :update do
      primary? true
      accept [:name, :description]
      require_atomic? false
      validate present([:name])
      validate one_of(:name, ["index", "edit", "create", "assign", "delete", "own"])
    end

    destroy :destroy do
      primary? true
    end
  end

  policies do
    # Only super admins can create/update/delete role types
    policy action_type([:create, :update, :destroy]) do
      forbid_if always()
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :name, :string do
      allow_nil? false
      public? true
    end

    attribute :description, :string do
      public? true
    end

    timestamps()
  end

  identities do
    identity :unique_name, [:name]
  end
end
