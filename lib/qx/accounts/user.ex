defmodule Qx.Accounts.User do
  use Ash.Resource,
    otp_app: :qx,
    domain: Qx.Accounts,
    data_layer: AshPostgres.DataLayer,
    authorizers: [Ash.Policy.Authorizer],
    extensions: [AshAuthentication, AshRbac]

  authentication do
    add_ons do
      log_out_everywhere do
        apply_on_password_change? true
      end
    end

    tokens do
      enabled? true
      token_resource Qx.Accounts.Token
      signing_secret Qx.Secrets
      store_all_tokens? true
      require_token_presence_for_authentication? true
    end

    strategies do
      magic_link do
        identity_field :email
        registration_enabled? true
        require_interaction? true

        sender Qx.Accounts.User.Senders.SendMagicLinkEmail
      end
    end
  end

  postgres do
    table "users"
    repo Qx.Repo
  end

  rbac do
    # Super admins can manage all users
    bypass :super_admin

    # Operators can read users in their account and update themselves
    role :operator do
      fields([:id, :email, :account_id, :roles])
      actions([:read])
    end
  end

  actions do
    defaults [:read]

    create :create do
      primary? true
      accept [:email, :account_id]

      # Assign default operator role to new users
      change Qx.Accounts.Changes.AssignDefaultRole
    end

    read :get_by_subject do
      description "Get a user by the subject claim in a JWT"
      argument :subject, :string, allow_nil?: false
      get? true
      prepare AshAuthentication.Preparations.FilterBySubject
    end

    read :get_by_email do
      description "Looks up a user by their email"
      get? true

      argument :email, :ci_string do
        allow_nil? false
      end

      filter expr(email == ^arg(:email))
    end

    read :list_by_account do
      description "List all users for a given account"
      filter expr(account_id == ^arg(:account_id))

      argument :account_id, :uuid do
        allow_nil? false
      end
    end

    create :sign_in_with_magic_link do
      description "Sign in or register a user with magic link."

      argument :token, :string do
        description "The token from the magic link that was sent to the user"
        allow_nil? false
      end

      upsert? true
      upsert_identity :unique_email
      upsert_fields [:email]

      # Uses the information from the token to create or sign in the user
      change AshAuthentication.Strategy.MagicLink.SignInChange

      # Assign default operator role to new users
      change Qx.Accounts.Changes.AssignDefaultRole

      metadata :token, :string do
        allow_nil? false
      end
    end

    action :request_magic_link do
      argument :email, :ci_string do
        allow_nil? false
      end

      run AshAuthentication.Strategy.MagicLink.Request
    end
  end

  policies do
    bypass AshAuthentication.Checks.AshAuthenticationInteraction do
      authorize_if always()
    end

    # Users can only access users in their own account
    policy action_type(:read) do
      authorize_if Qx.Accounts.Checks.SameAccount
    end

    # Users can update themselves, but not their roles (roles managed separately)
    policy action_type(:update) do
      authorize_if expr(id == ^actor(:id))
      forbid_if changing_attributes([:roles])
    end

    # Role management is restricted - only users with assign permissions can modify roles
    policy action_type(:update) do
      authorize_if changing_attributes([:roles])
      authorize_if Qx.Accounts.Checks.CanAssignRoles
    end

    # User creation is allowed for authenticated users in the same account
    policy action_type(:create) do
      authorize_if Qx.Accounts.Checks.SameAccount
    end

    # Only super admins can destroy users (handled by RBAC bypass)
    policy action_type(:destroy) do
      forbid_if always()
    end
  end

  attributes do
    uuid_v7_primary_key :id

    attribute :email, :ci_string do
      allow_nil? false
      public? true
    end

    attribute :roles, {:array, :atom} do
      public? true
      default [:operator]
    end
  end

  relationships do
    belongs_to :account, Qx.Accounts.Account do
      allow_nil? false
      public? true
    end

    has_many :user_roles, Qx.Accounts.Role do
      destination_attribute :user_id
      public? true
    end

    has_many :granted_roles, Qx.Accounts.Role do
      destination_attribute :granted_by_id
      public? true
    end
  end

  calculations do
    calculate :property_roles, :map do
      description "Map of property IDs to lists of role type names for this user"

      calculation fn records, _context ->
        user_ids = Enum.map(records, & &1.id)

        # Query all roles for these users
        roles_query = """
        SELECT r.user_id, r.property_id, rt.name as role_name
        FROM roles r
        JOIN role_types rt ON r.role_type_id = rt.id
        WHERE r.user_id = ANY($1)
        """

        case Qx.Repo.query(roles_query, [user_ids]) do
          {:ok, %{rows: rows}} ->
            # Group roles by user_id and property_id
            roles_by_user =
              rows
              |> Enum.group_by(fn [user_id, _property_id, _role_name] -> user_id end)
              |> Map.new(fn {user_id, user_roles} ->
                property_roles =
                  user_roles
                  |> Enum.group_by(fn [_user_id, property_id, _role_name] -> property_id end)
                  |> Map.new(fn {property_id, property_role_rows} ->
                    role_names =
                      Enum.map(property_role_rows, fn [_user_id, _property_id, role_name] ->
                        role_name
                      end)

                    {property_id, role_names}
                  end)

                {user_id, property_roles}
              end)

            # Return property_roles for each user
            Enum.map(records, fn user ->
              Map.get(roles_by_user, user.id, %{})
            end)

          _ ->
            # Return empty maps if query fails
            Enum.map(records, fn _user -> %{} end)
        end
      end
    end
  end

  identities do
    identity :unique_email, [:email]
    identity :by_account, [:account_id]
  end
end
