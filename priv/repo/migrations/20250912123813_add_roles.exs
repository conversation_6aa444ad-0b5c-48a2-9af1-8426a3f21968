defmodule Qx.Repo.Migrations.AddRoles do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:roles, primary_key: false) do
      add :id, :uuid, null: false, default: fragment("uuid_generate_v7()"), primary_key: true
      add :granted_at, :utc_datetime, null: false, default: fragment("(now() AT TIME ZONE 'utc')")

      add :inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")

      add :user_id,
          references(:users,
            column: :id,
            name: "roles_user_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :property_id,
          references(:properties,
            column: :id,
            name: "roles_property_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :role_type_id,
          references(:role_types,
            column: :id,
            name: "roles_role_type_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false

      add :granted_by_id,
          references(:users,
            column: :id,
            name: "roles_granted_by_id_fkey",
            type: :uuid,
            prefix: "public"
          )
    end

    create unique_index(:roles, [:user_id, :property_id, :role_type_id],
             name: "roles_unique_user_property_role_type_index"
           )
  end

  def down do
    drop_if_exists unique_index(:roles, [:user_id, :property_id, :role_type_id],
                     name: "roles_unique_user_property_role_type_index"
                   )

    drop constraint(:roles, "roles_user_id_fkey")

    drop constraint(:roles, "roles_property_id_fkey")

    drop constraint(:roles, "roles_role_type_id_fkey")

    drop constraint(:roles, "roles_granted_by_id_fkey")

    drop table(:roles)
  end
end
