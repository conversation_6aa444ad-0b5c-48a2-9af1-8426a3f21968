defmodule Qx.Repo.Migrations.AddRolesToUsers do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:users) do
      add :roles, {:array, :text}, default: fragment("'{operator}'")
    end
  end

  def down do
    alter table(:users) do
      remove :roles
    end
  end
end
