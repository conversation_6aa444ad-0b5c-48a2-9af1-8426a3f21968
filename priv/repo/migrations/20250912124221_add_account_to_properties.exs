defmodule Qx.Repo.Migrations.AddAccountToProperties do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:properties) do
      add :account_id,
          references(:accounts,
            column: :id,
            name: "properties_account_id_fkey",
            type: :uuid,
            prefix: "public"
          ),
          null: false
    end
  end

  def down do
    drop constraint(:properties, "properties_account_id_fkey")

    alter table(:properties) do
      remove :account_id
    end
  end
end
