defmodule Qx.Repo.Migrations.AssignDefaultRolesToExistingUsers do
  @moduledoc """
  Data migration to assign the default 'operator' role to existing users
  who don't already have it in their roles array.
  """

  use Ecto.Migration

  def up do
    # Update users who don't have 'operator' in their roles array
    execute """
    UPDATE users 
    SET roles = array_append(roles, 'operator')
    WHERE NOT ('operator' = ANY(roles))
    """
  end

  def down do
    # Remove 'operator' role from users who only have that role
    # (i.e., users who got it from this migration)
    execute """
    UPDATE users 
    SET roles = array_remove(roles, 'operator')
    WHERE roles = '{operator}'
    """
  end
end
