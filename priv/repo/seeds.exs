# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     Qx.Repo.insert!(%Qx.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

import Ecto.Query
import Ash.Query, only: [filter: 2]
require Ash.Query

# Create the six required role types for the RBAC system
role_types = [
  %{
    name: "index",
    description: "Can view and list properties and their basic information"
  },
  %{
    name: "edit",
    description: "Can modify property details, settings, and configuration"
  },
  %{
    name: "create",
    description: "Can create new properties within the account"
  },
  %{
    name: "assign",
    description: "Can assign and manage roles for other users on properties"
  },
  %{
    name: "delete",
    description: "Can delete properties and their associated data"
  },
  %{
    name: "own",
    description: "Has full ownership and control over the property, including all permissions"
  }
]

IO.puts("Creating role types...")

Enum.each(role_types, fn role_type_attrs ->
  # Try to create the role type, bypassing authorization for seeds
  case Qx.Accounts.RoleType
       |> Ash.Changeset.for_create(:create, role_type_attrs)
       |> Ash.create(authorize?: false) do
    {:ok, role_type} ->
      IO.puts("  ✓ Created role type: #{role_type.name}")

    {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Changes.InvalidAttribute{field: :name}]}} ->
      IO.puts("  - Role type already exists: #{role_type_attrs.name}")

    {:error, error} ->
      IO.puts("  ✗ Failed to create role type #{role_type_attrs.name}: #{inspect(error)}")
  end
end)

IO.puts("Seed data creation completed!")
