defmodule Qx.Accounts.RbacTest do
  @moduledoc """
  Comprehensive test suite for the RBAC (Role-Based Access Control) system.

  Tests cover:
  - Role type creation and validation
  - Role assignment and management
  - Property-scoped permissions
  - Account-level isolation
  - Policy enforcement
  - Edge cases and security boundaries
  """

  use Qx.DataCase, async: true

  import Ash.Query, only: [filter: 2]
  require Ash.Query

  alias Qx.Accounts.{Account, User, Property, Role, RoleType}

  describe "RoleType resource" do
    test "creates valid role types" do
      valid_role_types = ["index", "edit", "create", "assign", "delete", "own"]

      for role_name <- valid_role_types do
        assert {:ok, role_type} =
                 RoleType
                 |> Ash.Changeset.for_create(:create, %{
                   name: role_name,
                   description: "Test #{role_name} role"
                 })
                 |> Ash.create(authorize?: false)

        assert role_type.name == role_name
      end
    end

    test "rejects invalid role types" do
      assert {:error, %Ash.Error.Invalid{}} =
               RoleType
               |> Ash.Changeset.for_create(:create, %{
                 name: "invalid_role",
                 description: "Invalid role"
               })
               |> Ash.create(authorize?: false)
    end

    test "enforces unique role type names" do
      # Create first role type
      assert {:ok, _role_type} =
               RoleType
               |> Ash.Changeset.for_create(:create, %{
                 name: "index",
                 description: "First index role"
               })
               |> Ash.create(authorize?: false)

      # Try to create duplicate
      assert {:error, %Ash.Error.Invalid{}} =
               RoleType
               |> Ash.Changeset.for_create(:create, %{
                 name: "index",
                 description: "Duplicate index role"
               })
               |> Ash.create(authorize?: false)
    end
  end

  describe "Role assignment" do
    setup do
      # Create test data
      {:ok, account} =
        Account
        |> Ash.Changeset.for_create(:create, %{
          name: "Test Account",
          description: "Test account for RBAC"
        })
        |> Ash.create(authorize?: false)

      {:ok, user} =
        User
        |> Ash.Changeset.for_create(:create, %{
          email: "<EMAIL>",
          account_id: account.id
        })
        |> Ash.create(authorize?: false)

      {:ok, property} =
        Property
        |> Ash.Changeset.for_create(:create, %{
          title: "Test Property",
          name: "test-property",
          account_id: account.id
        })
        |> Ash.create(authorize?: false)

      {:ok, role_type} =
        RoleType
        |> Ash.Changeset.for_create(:create, %{
          name: "index",
          description: "Index permission"
        })
        |> Ash.create(authorize?: false)

      %{
        account: account,
        user: user,
        property: property,
        role_type: role_type
      }
    end

    test "creates role assignment", %{user: user, property: property, role_type: role_type} do
      assert {:ok, role} =
               Role
               |> Ash.Changeset.for_create(:assign_role, %{
                 user_id: user.id,
                 property_id: property.id,
                 role_type_id: role_type.id
               })
               |> Ash.create(authorize?: false)

      assert role.user_id == user.id
      assert role.property_id == property.id
      assert role.role_type_id == role_type.id
    end

    test "enforces unique role assignments", %{
      user: user,
      property: property,
      role_type: role_type
    } do
      # Create first role assignment
      assert {:ok, _role} =
               Role
               |> Ash.Changeset.for_create(:assign_role, %{
                 user_id: user.id,
                 property_id: property.id,
                 role_type_id: role_type.id
               })
               |> Ash.create(authorize?: false)

      # Try to create duplicate
      assert {:error, %Ash.Error.Invalid{}} =
               Role
               |> Ash.Changeset.for_create(:assign_role, %{
                 user_id: user.id,
                 property_id: property.id,
                 role_type_id: role_type.id
               })
               |> Ash.create(authorize?: false)
    end
  end

  describe "Default role assignment" do
    test "assigns operator role to new users" do
      {:ok, account} =
        Account
        |> Ash.Changeset.for_create(:create, %{
          name: "Test Account",
          description: "Test account"
        })
        |> Ash.create(authorize?: false)

      {:ok, user} =
        User
        |> Ash.Changeset.for_create(:create, %{
          email: "<EMAIL>",
          account_id: account.id
        })
        |> Ash.create(authorize?: false)

      assert :operator in user.roles
    end
  end

  describe "Account isolation" do
    setup do
      # Create two separate accounts with users and properties
      {:ok, account1} =
        Account
        |> Ash.Changeset.for_create(:create, %{
          name: "Account 1",
          description: "First account"
        })
        |> Ash.create(authorize?: false)

      {:ok, account2} =
        Account
        |> Ash.Changeset.for_create(:create, %{
          name: "Account 2",
          description: "Second account"
        })
        |> Ash.create(authorize?: false)

      {:ok, user1} =
        User
        |> Ash.Changeset.for_create(:create, %{
          email: "<EMAIL>",
          account_id: account1.id
        })
        |> Ash.create(authorize?: false)

      {:ok, user2} =
        User
        |> Ash.Changeset.for_create(:create, %{
          email: "<EMAIL>",
          account_id: account2.id
        })
        |> Ash.create(authorize?: false)

      %{
        account1: account1,
        account2: account2,
        user1: user1,
        user2: user2
      }
    end

    test "users can only access their own account", %{
      account1: account1,
      account2: account2,
      user1: user1
    } do
      # User1 should be able to read their own account
      assert {:ok, account} = Ash.get(Account, account1.id, actor: user1)
      assert account.id == account1.id

      # User1 should not be able to read account2 (should get not found due to policy filtering)
      assert {:error, %Ash.Error.Invalid{errors: [%Ash.Error.Query.NotFound{}]}} =
               Ash.get(Account, account2.id, actor: user1)
    end
  end
end
